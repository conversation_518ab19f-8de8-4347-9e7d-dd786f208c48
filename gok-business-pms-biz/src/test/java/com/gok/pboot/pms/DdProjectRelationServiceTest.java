package com.gok.pboot.pms;

import com.gok.pboot.pms.entity.domain.DdProjectRelation;
import com.gok.pboot.pms.service.IDdProjectRelationService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 滴滴项目关联关系服务测试
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class DdProjectRelationServiceTest {

    @Resource
    private IDdProjectRelationService ddProjectRelationService;

    @Test
    public void testSaveOrUpdateRelation() {
        // 测试数据
        Long projectId = 1001L;
        String projectNo = "TEST-PROJECT-001";
        String didiProjectId = "DIDI-PROJECT-001";

        // 测试保存关联关系
        boolean result = ddProjectRelationService.saveOrUpdateRelation(projectId, projectNo, didiProjectId);
        log.info("保存关联关系结果: {}", result);

        // 验证保存结果
        DdProjectRelation relation = ddProjectRelationService.getByProjectId(projectId);
        if (relation != null) {
            log.info("查询到的关联关系: 项目ID={}, 项目编码={}, 滴滴项目ID={}, 同步时间={}", 
                    relation.getProjectId(), relation.getProjectNo(), 
                    relation.getDidiProjectId(), relation.getSyncTime());
        } else {
            log.warn("未查询到关联关系");
        }

        // 测试更新关联关系
        String newDidiProjectId = "DIDI-PROJECT-002";
        boolean updateResult = ddProjectRelationService.saveOrUpdateRelation(projectId, projectNo, newDidiProjectId);
        log.info("更新关联关系结果: {}", updateResult);

        // 验证更新结果
        DdProjectRelation updatedRelation = ddProjectRelationService.getByProjectId(projectId);
        if (updatedRelation != null) {
            log.info("更新后的关联关系: 项目ID={}, 项目编码={}, 滴滴项目ID={}, 同步时间={}", 
                    updatedRelation.getProjectId(), updatedRelation.getProjectNo(), 
                    updatedRelation.getDidiProjectId(), updatedRelation.getSyncTime());
        }
    }

    @Test
    public void testUpdateSyncTime() {
        // 测试数据
        Long projectId = 1001L;
        String projectNo = "TEST-PROJECT-001";
        String didiProjectId = "DIDI-PROJECT-001";

        // 先保存一个关联关系
        ddProjectRelationService.saveOrUpdateRelation(projectId, projectNo, didiProjectId);

        // 等待一秒，确保时间有差异
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 测试更新同步时间
        boolean result = ddProjectRelationService.updateSyncTime(projectId);
        log.info("更新同步时间结果: {}", result);

        // 验证更新结果
        DdProjectRelation relation = ddProjectRelationService.getByProjectId(projectId);
        if (relation != null) {
            log.info("更新同步时间后的关联关系: 项目ID={}, 同步时间={}", 
                    relation.getProjectId(), relation.getSyncTime());
        }
    }

    @Test
    public void testQueryMethods() {
        // 测试数据
        Long projectId = 1002L;
        String projectNo = "TEST-PROJECT-002";
        String didiProjectId = "DIDI-PROJECT-002";

        // 保存测试数据
        ddProjectRelationService.saveOrUpdateRelation(projectId, projectNo, didiProjectId);

        // 测试根据项目ID查询
        DdProjectRelation relationById = ddProjectRelationService.getByProjectId(projectId);
        log.info("根据项目ID查询结果: {}", relationById);

        // 测试根据项目编码查询
        DdProjectRelation relationByNo = ddProjectRelationService.getByProjectNo(projectNo);
        log.info("根据项目编码查询结果: {}", relationByNo);

        // 测试根据滴滴项目ID查询
        DdProjectRelation relationByDidiId = ddProjectRelationService.getByDidiProjectId(didiProjectId);
        log.info("根据滴滴项目ID查询结果: {}", relationByDidiId);
    }

}
