# 滴滴企业版配置示例
didi:
  client:
    # 客户端ID，从滴滴企业版后台获取
    id: ${DIDI_CLIENT_ID:your_client_id}
    # 客户端密钥，从滴滴企业版后台获取
    secret: ${DIDI_CLIENT_SECRET:your_client_secret}
    # 授权类型
    grant-type: client_credentials
  
  api:
    # 滴滴API基础URL
    base-url: https://api.es.xiaojukeji.com
    # 请求超时时间（毫秒）
    timeout: 30000
    # 重试次数
    retry-count: 3
  
  sync:
    # 是否启用自动同步
    enabled: false
    # 同步间隔（分钟）
    interval: 30
    # 批量同步大小
    batch-size: 100
    # 是否启用异步同步
    async: true
    # 用户同步间隔（毫秒）- 滴滴要求连续添加员工间隔150ms
    user:
      interval: 150

# 日志配置
logging:
  level:
    com.gok.pboot.pms.didi: DEBUG 