<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.DdProjectRelationMapper">

    <!-- 通用结果映射 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.domain.DdProjectRelation">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="project_no" property="projectNo" />
        <result column="didi_project_id" property="didiProjectId" />
        <result column="sync_time" property="syncTime" />
    </resultMap>

    <!-- 通用字段 -->
    <sql id="Base_Column_List">
        id, project_id, project_no, didi_project_id, sync_time
    </sql>

    <!-- 根据项目ID查询关联关系 -->
    <select id="selectByProjectId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dd_project_relation
        WHERE project_id = #{projectId}
        LIMIT 1
    </select>

    <!-- 根据项目编码查询关联关系 -->
    <select id="selectByProjectNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dd_project_relation
        WHERE project_no = #{projectNo}
        LIMIT 1
    </select>

    <!-- 根据滴滴项目ID查询关联关系 -->
    <select id="selectByDidiProjectId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dd_project_relation
        WHERE didi_project_id = #{didiProjectId}
        LIMIT 1
    </select>

    <!-- 更新同步时间 -->
    <update id="updateSyncTimeByProjectId">
        UPDATE dd_project_relation
        SET sync_time = NOW()
        WHERE project_id = #{projectId}
    </update>

</mapper>
