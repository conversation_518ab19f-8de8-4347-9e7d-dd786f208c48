<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.DdOrderMapper">

    <!-- 机票订单查询结果列 -->
    <sql id="FlightTicket_Column_List">
        o.id, o.order_no, o.passenger_id, o.passenger_employee_no, o.passenger_name, o.passenger_dept_id, o.passenger_dept_name,
        o.ticket_no, o.ticket_status, o.departure_location, o.arrival_location, o.flight_no, o.departure_time, o.landing_time,
        o.company_actual_payment, o.service_fee, o.booking_date, o.booking_user_id, o.booking_employee_no, o.booking_employee_name,
        o.booking_dept_id, o.booking_dept_name, o.business_trip_application_no, o.business_trip_reason, o.cost_center_id,
        o.cost_center_name, o.project_id, o.project_name, o.project_code, o.company_id, o.company_name, o.accounting_period,
        o.expense_report_no, o.initiation_status, o.initiation_date, o.request_id, o.creator, o.creator_id, o.modifier, o.modifier_id, o.ctime, o.mtime, o.del_flag,
        p.project_status
    </sql>

    <!-- 酒店订单查询结果列 -->
    <sql id="Hotel_Column_List">
        o.id, o.order_no, o.checkin_user_id, o.checkin_employee_no, o.checkin_person_name, o.checkin_dept_id, o.checkin_dept_name,
        o.city_name, o.hotel_name, o.room_type, o.checkin_time, o.checkout_time, o.company_actual_payment, o.service_fee,
        o.number_of_days, o.number_of_rooms, o.room_nights, o.unit_price, o.room_standard_difference, o.order_status,
        o.booking_date, o.booking_user_id, o.booking_employee_no, o.booking_employee_name, o.booking_dept_id, o.booking_dept_name,
        o.business_trip_application_no, o.business_trip_reason, o.cost_center_id, o.cost_center_name, o.project_id, o.project_name,
        o.project_code, o.company_id, o.company_name, o.accounting_period, o.expense_report_no, o.initiation_status, o.initiation_date, o.request_id,
        o.creator, o.creator_id, o.modifier, o.modifier_id, o.ctime, o.mtime, o.del_flag,
        p.project_status
    </sql>

    <!-- 用车订单查询结果列 -->
    <sql id="Vehicle_Column_List">
        o.id, o.order_no, o.passenger_id, o.passenger_employee_no, o.passenger_name, o.passenger_dept_id, o.passenger_dept_name,
        o.travel_time, o.arrival_time, o.vehicle_type, o.departure_city, o.departure_address, o.arrival_city, o.arrival_address,
        o.travel_distance, o.company_actual_payment, o.service_fee, o.payment_type, o.booking_date, o.booking_user_id,
        o.booking_employee_no, o.booking_employee_name, o.booking_dept_id, o.booking_dept_name, o.business_trip_application_no,
        o.business_trip_reason, o.cost_center_id, o.cost_center_name, o.project_id, o.project_name, o.project_code, o.company_id,
        o.company_name, o.accounting_period, o.expense_report_no, o.initiation_status, o.initiation_date, o.request_id, o.creator, o.creator_id,
        o.modifier, o.modifier_id, o.ctime, o.mtime, o.del_flag,
        p.project_status
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Common_Where_Condition">
        WHERE o.del_flag = 0
        <if test="queryDTO != null">
            <if test="queryDTO.employeeNoOrName != null and queryDTO.employeeNoOrName != ''">
                AND (o.passenger_employee_no LIKE CONCAT('%', #{queryDTO.employeeNoOrName}, '%')
                OR o.passenger_name LIKE CONCAT('%', #{queryDTO.employeeNoOrName}, '%')
                OR o.booking_employee_no LIKE CONCAT('%', #{queryDTO.employeeNoOrName}, '%')
                OR o.booking_employee_name LIKE CONCAT('%', #{queryDTO.employeeNoOrName}, '%'))
            </if>
            <if test="queryDTO.orderNoOrExpenseReportNo != null and queryDTO.orderNoOrExpenseReportNo != ''">
                AND (o.order_no LIKE CONCAT('%', #{queryDTO.orderNoOrExpenseReportNo}, '%')
                OR o.expense_report_no LIKE CONCAT('%', #{queryDTO.orderNoOrExpenseReportNo}, '%'))
            </if>

            <if test="queryDTO.initiationStatues != null and queryDTO.initiationStatues.size() > 0">
                AND o.initiation_status in
                <foreach item="item" collection="queryDTO.initiationStatues" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="queryDTO.companyIds != null and queryDTO.companyIds.size() > 0">
                AND o.company_id in
                <foreach item="item" collection="queryDTO.companyIds" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="queryDTO.costCenterIds != null and queryDTO.costCenterIds.size() > 0">
                AND o.cost_center_id in
                <foreach item="item" collection="queryDTO.costCenterIds" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryDTO.projectNameOrCode != null and queryDTO.projectNameOrCode != ''">
                AND (o.project_name LIKE CONCAT('%', #{queryDTO.projectNameOrCode}, '%')
                OR o.project_code LIKE CONCAT('%', #{queryDTO.projectNameOrCode}, '%'))
            </if>
            <if test="queryDTO.accountingPeriod != null and queryDTO.accountingPeriod != ''">
                AND o.accounting_period = #{queryDTO.accountingPeriod}
            </if>
        </if>
        <if test="queryDTO.scope != null and queryDTO.scope">
            and (
                <if test="queryDTO.userIdList != null and queryDTO.userIdList.size() > 0">
                    o.booking_user_id in
                    <foreach item="item" collection="queryDTO.userIdList" separator="," open="(" close=")">
                        #{item}
                    </foreach>

                    <if test="queryDTO.orderType == 'HOTEL'">
                        or o.checkin_user_id in
                        <foreach item="item" collection="queryDTO.userIdList" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryDTO.orderType != 'HOTEL'">
                        or o.passenger_id in
                        <foreach item="item" collection="queryDTO.userIdList" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
                </if>

                <if test="queryDTO.subjectNames != null and queryDTO.subjectNames.size() > 0">
                    <if test="queryDTO.userIdList != null and queryDTO.userIdList.size() > 0">
                        or
                    </if>
                    o.project_name in
                    <foreach item="item" collection="queryDTO.subjectNames" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
            )
        </if>
    </sql>

    <!-- 可发起报销的查询条件 -->
    <sql id="Can_Initiate_Where_Condition">
        WHERE o.del_flag = 0
        AND o.initiation_status IN (0, 2, 3)  <!-- 待发起、发起异常-OA接口报错、发起异常-预算不足 -->
        <if test="queryDTO != null">
            <if test="queryDTO.employeeNoOrName != null and queryDTO.employeeNoOrName != ''">
                AND (o.passenger_employee_no LIKE CONCAT('%', #{queryDTO.employeeNoOrName}, '%')
                OR o.passenger_name LIKE CONCAT('%', #{queryDTO.employeeNoOrName}, '%')
                OR o.booking_employee_no LIKE CONCAT('%', #{queryDTO.employeeNoOrName}, '%')
                OR o.booking_employee_name LIKE CONCAT('%', #{queryDTO.employeeNoOrName}, '%'))
            </if>
            <if test="queryDTO.orderNoOrExpenseReportNo != null and queryDTO.orderNoOrExpenseReportNo != ''">
                AND (o.order_no LIKE CONCAT('%', #{queryDTO.orderNoOrExpenseReportNo}, '%')
                OR o.expense_report_no LIKE CONCAT('%', #{queryDTO.orderNoOrExpenseReportNo}, '%'))
            </if>
            <if test="queryDTO.initiationStatues != null and queryDTO.initiationStatues.size() > 0">
                AND o.initiation_status in
                <foreach item="item" collection="queryDTO.initiationStatues" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="queryDTO.companyIds != null and queryDTO.companyIds.size() > 0">
                AND o.company_id in
                <foreach item="item" collection="queryDTO.companyIds" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="queryDTO.costCenterIds != null and queryDTO.costCenterIds.size() > 0">
                AND o.cost_center_id in
                <foreach item="item" collection="queryDTO.costCenterIds" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryDTO.projectNameOrCode != null and queryDTO.projectNameOrCode != ''">
                AND (o.project_name LIKE CONCAT('%', #{queryDTO.projectNameOrCode}, '%')
                OR o.project_code LIKE CONCAT('%', #{queryDTO.projectNameOrCode}, '%'))
            </if>
            <if test="queryDTO.accountingPeriod != null and queryDTO.accountingPeriod != ''">
                AND o.accounting_period = #{queryDTO.accountingPeriod}
            </if>
        </if>
        <if test="selectedIds != null and selectedIds.size() > 0">
            AND o.id IN
            <foreach collection="selectedIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </sql>

    <!-- 分页查询机票订单 -->
    <select id="findFlightTicketPageList" resultType="com.gok.pboot.pms.entity.vo.DdFlightTicketOrderFindPageVO">
        SELECT
        <include refid="FlightTicket_Column_List"/>
        FROM dd_flight_ticket_order o
        LEFT JOIN project_info p ON o.project_id = p.id
        <include refid="Common_Where_Condition"/>
        ORDER BY o.ctime DESC
    </select>

    <!-- 分页查询酒店订单 -->
    <select id="findHotelPageList" resultType="com.gok.pboot.pms.entity.vo.DdHotelOrderFindPageVO">
        SELECT
        <include refid="Hotel_Column_List"/>
        FROM dd_hotel_order o
        LEFT JOIN project_info p ON o.project_id = p.id
        <include refid="Common_Where_Condition"/>
        ORDER BY o.ctime DESC
    </select>

    <!-- 分页查询用车订单 -->
    <select id="findVehiclePageList" resultType="com.gok.pboot.pms.entity.vo.DdVehicleOrderFindPageVO">
        SELECT
        <include refid="Vehicle_Column_List"/>
        FROM dd_vehicle_order o
        LEFT JOIN project_info p ON o.project_id = p.id
        <include refid="Common_Where_Condition"/>
        ORDER BY o.ctime DESC
    </select>

    <!-- 查询可发起报销的机票订单 -->
    <select id="findCanInitiateFlightTicketOrders" resultType="com.gok.pboot.pms.entity.DdFlightTicketOrder">
        SELECT
        <include refid="FlightTicket_Column_List"/>
        FROM dd_flight_ticket_order o
        LEFT JOIN project_info p ON o.project_id = p.id
        <include refid="Can_Initiate_Where_Condition"/>
        ORDER BY o.ctime DESC
    </select>

    <!-- 查询可发起报销的酒店订单 -->
    <select id="findCanInitiateHotelOrders" resultType="com.gok.pboot.pms.entity.DdHotelOrder">
        SELECT
        <include refid="Hotel_Column_List"/>
        FROM dd_hotel_order o
        LEFT JOIN project_info p ON o.project_id = p.id
        <include refid="Can_Initiate_Where_Condition"/>
        ORDER BY o.ctime DESC
    </select>

    <!-- 查询可发起报销的用车订单 -->
    <select id="findCanInitiateVehicleOrders" resultType="com.gok.pboot.pms.entity.DdVehicleOrder">
        SELECT
        <include refid="Vehicle_Column_List"/>
        FROM dd_vehicle_order o
        LEFT JOIN project_info p ON o.project_id = p.id
        <include refid="Can_Initiate_Where_Condition"/>
        ORDER BY o.ctime DESC
    </select>

    <!-- 批量插入机票订单 -->
    <insert id="batchInsertFlightTicketOrders" parameterType="java.util.List">
        INSERT INTO dd_flight_ticket_order (
            id, order_no, passenger_id, passenger_employee_no, passenger_name, passenger_dept_id, passenger_dept_name,
            ticket_no, ticket_status, departure_location, arrival_location, flight_no, departure_time, landing_time,
            company_actual_payment, service_fee, booking_date, booking_user_id, booking_employee_no, booking_employee_name,
            booking_dept_id, booking_dept_name, business_trip_application_no, business_trip_reason, cost_center_id,
            cost_center_name, project_id, project_name, project_code, company_id, company_name, accounting_period,
            expense_report_no, initiation_status, initiation_date, request_id, creator, creator_id, modifier, modifier_id, ctime, mtime, del_flag
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.orderNo}, #{item.passengerId}, #{item.passengerEmployeeNo}, #{item.passengerName}, #{item.passengerDeptId}, #{item.passengerDeptName},
                #{item.ticketNo}, #{item.ticketStatus}, #{item.departureLocation}, #{item.arrivalLocation}, #{item.flightNo}, #{item.departureTime}, #{item.landingTime},
                #{item.companyActualPayment}, #{item.serviceFee}, #{item.bookingDate}, #{item.bookingUserId}, #{item.bookingEmployeeNo}, #{item.bookingEmployeeName},
                #{item.bookingDeptId}, #{item.bookingDeptName}, #{item.businessTripApplicationNo}, #{item.businessTripReason}, #{item.costCenterId},
                #{item.costCenterName}, #{item.projectId}, #{item.projectName}, #{item.projectCode}, #{item.companyId}, #{item.companyName}, #{item.accountingPeriod},
                #{item.expenseReportNo}, #{item.initiationStatus}, #{item.initiationDate}, #{item.requestId}, #{item.creator}, #{item.creatorId}, #{item.modifier}, #{item.modifierId}, #{item.ctime}, #{item.mtime}, #{item.delFlag}
            )
        </foreach>
    </insert>

    <!-- 批量插入酒店订单 -->
    <insert id="batchInsertHotelOrders" parameterType="java.util.List">
        INSERT INTO dd_hotel_order (
            id, order_no, checkin_user_id, checkin_employee_no, checkin_person_name, checkin_dept_id, checkin_dept_name,
            city_name, hotel_name, room_type, checkin_time, checkout_time, company_actual_payment, service_fee,
            number_of_days, number_of_rooms, room_nights, unit_price, room_standard_difference, order_status,
            booking_date, booking_user_id, booking_employee_no, booking_employee_name, booking_dept_id, booking_dept_name,
            business_trip_application_no, business_trip_reason, cost_center_id, cost_center_name, project_id, project_name,
            project_code, company_id, company_name, accounting_period, expense_report_no, initiation_status, initiation_date, request_id,
            creator, creator_id, modifier, modifier_id, ctime, mtime, del_flag
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.orderNo}, #{item.checkinUserId}, #{item.checkinEmployeeNo}, #{item.checkinPersonName}, #{item.checkinDeptId}, #{item.checkinDeptName},
                #{item.cityName}, #{item.hotelName}, #{item.roomType}, #{item.checkinTime}, #{item.checkoutTime}, #{item.companyActualPayment}, #{item.serviceFee},
                #{item.numberOfDays}, #{item.numberOfRooms}, #{item.roomNights}, #{item.unitPrice}, #{item.roomStandardDifference}, #{item.orderStatus},
                #{item.bookingDate}, #{item.bookingUserId}, #{item.bookingEmployeeNo}, #{item.bookingEmployeeName}, #{item.bookingDeptId}, #{item.bookingDeptName},
                #{item.businessTripApplicationNo}, #{item.businessTripReason}, #{item.costCenterId}, #{item.costCenterName}, #{item.projectId}, #{item.projectName},
                #{item.projectCode}, #{item.companyId}, #{item.companyName}, #{item.accountingPeriod}, #{item.expenseReportNo}, #{item.initiationStatus}, #{item.initiationDate}, #{item.requestId},
                #{item.creator}, #{item.creatorId}, #{item.modifier}, #{item.modifierId}, #{item.ctime}, #{item.mtime}, #{item.delFlag}
            )
        </foreach>
    </insert>

    <!-- 批量插入用车订单 -->
    <insert id="batchInsertVehicleOrders" parameterType="java.util.List">
        INSERT INTO dd_vehicle_order (
            id, order_no, passenger_id, passenger_employee_no, passenger_name, passenger_dept_id, passenger_dept_name,
            travel_time, arrival_time, vehicle_type, departure_city, departure_address, arrival_city, arrival_address,
            travel_distance, company_actual_payment, service_fee, payment_type, booking_date, booking_user_id,
            booking_employee_no, booking_employee_name, booking_dept_id, booking_dept_name, business_trip_application_no,
            business_trip_reason, cost_center_id, cost_center_name, project_id, project_name, project_code, company_id,
            company_name, accounting_period, expense_report_no, initiation_status, initiation_date, request_id, creator, creator_id,
            modifier, modifier_id, ctime, mtime, del_flag
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.orderNo}, #{item.passengerId}, #{item.passengerEmployeeNo}, #{item.passengerName}, #{item.passengerDeptId}, #{item.passengerDeptName},
                #{item.travelTime}, #{item.arrivalTime}, #{item.vehicleType}, #{item.departureCity}, #{item.departureAddress}, #{item.arrivalCity}, #{item.arrivalAddress},
                #{item.travelDistance}, #{item.companyActualPayment}, #{item.serviceFee}, #{item.paymentType}, #{item.bookingDate}, #{item.bookingUserId},
                #{item.bookingEmployeeNo}, #{item.bookingEmployeeName}, #{item.bookingDeptId}, #{item.bookingDeptName}, #{item.businessTripApplicationNo},
                #{item.businessTripReason}, #{item.costCenterId}, #{item.costCenterName}, #{item.projectId}, #{item.projectName}, #{item.projectCode}, #{item.companyId},
                #{item.companyName}, #{item.accountingPeriod}, #{item.expenseReportNo}, #{item.initiationStatus}, #{item.initiationDate}, #{item.requestId}, #{item.creator}, #{item.creatorId},
                #{item.modifier}, #{item.modifierId}, #{item.ctime}, #{item.mtime}, #{item.delFlag}
            )
        </foreach>
    </insert>
    <update id="batchUpdateFlightTicketOrder">
        <foreach collection="list" item="item" separator=";">
            UPDATE dd_flight_ticket_order
            SET
            passenger_id = #{item.passengerId},
            passenger_employee_no = #{item.passengerEmployeeNo},
            passenger_name = #{item.passengerName},
            passenger_dept_id = #{item.passengerDeptId},
            passenger_dept_name = #{item.passengerDeptName},
            ticket_no = #{item.ticketNo},
            ticket_status = #{item.ticketStatus},
            departure_location = #{item.departureLocation},
            arrival_location = #{item.arrivalLocation},
            flight_no = #{item.flightNo},
            departure_time = #{item.departureTime},
            landing_time = #{item.landingTime},
            company_actual_payment = #{item.companyActualPayment},
            service_fee = #{item.serviceFee},
            booking_date = #{item.bookingDate},
            booking_user_id = #{item.bookingUserId},
            booking_employee_no = #{item.bookingEmployeeNo},
            booking_employee_name = #{item.bookingEmployeeName},
            booking_dept_id = #{item.bookingDeptId},
            booking_dept_name = #{item.bookingDeptName},
            business_trip_application_no = #{item.businessTripApplicationNo},
            business_trip_reason = #{item.businessTripReason},
            cost_center_id = #{item.costCenterId},
            cost_center_name = #{item.costCenterName},
            project_id = #{item.projectId},
            project_name = #{item.projectName},
            project_code = #{item.projectCode},
            company_id = #{item.companyId},
            company_name = #{item.companyName},
            accounting_period = #{item.accountingPeriod},
            expense_report_no = #{item.expenseReportNo},
            initiation_status = #{item.initiationStatus},
            initiation_date = #{item.initiationDate},
            request_id = #{item.requestId},
            creator = #{item.creator},
            creator_id = #{item.creatorId},
            modifier = #{item.modifier},
            modifier_id = #{item.modifierId},
            ctime = #{item.ctime},
            mtime = #{item.mtime}
            WHERE order_no = #{item.orderNo}
        </foreach>
    </update>

    <update id="batchUpdateHotelOrder">
        <foreach collection="list" item="item" separator=";">
            UPDATE dd_hotel_order
            SET
            checkin_user_id = #{item.checkinUserId},
            checkin_employee_no = #{item.checkinEmployeeNo},
            checkin_person_name = #{item.checkinPersonName},
            checkin_dept_id = #{item.checkinDeptId},
            checkin_dept_name = #{item.checkinDeptName},
            city_name = #{item.cityName},
            hotel_name = #{item.hotelName},
            room_type = #{item.roomType},
            checkin_time = #{item.checkinTime},
            checkout_time = #{item.checkoutTime},
            company_actual_payment = #{item.companyActualPayment},
            service_fee = #{item.serviceFee},
            number_of_days = #{item.numberOfDays},
            number_of_rooms = #{item.numberOfRooms},
            room_nights = #{item.roomNights},
            unit_price = #{item.unitPrice},
            room_standard_difference = #{item.roomStandardDifference},
            order_status = #{item.orderStatus},
            booking_date = #{item.bookingDate},
            booking_user_id = #{item.bookingUserId},
            booking_employee_no = #{item.bookingEmployeeNo},
            booking_employee_name = #{item.bookingEmployeeName},
            booking_dept_id = #{item.bookingDeptId},
            booking_dept_name = #{item.bookingDeptName},
            business_trip_application_no = #{item.businessTripApplicationNo},
            business_trip_reason = #{item.businessTripReason},
            cost_center_id = #{item.costCenterId},
            cost_center_name = #{item.costCenterName},
            project_id = #{item.projectId},
            project_name = #{item.projectName},
            project_code = #{item.projectCode},
            company_id = #{item.companyId},
            company_name = #{item.companyName},
            accounting_period = #{item.accountingPeriod},
            expense_report_no = #{item.expenseReportNo},
            initiation_status = #{item.initiationStatus},
            initiation_date = #{item.initiationDate},
            request_id = #{item.requestId},
            creator = #{item.creator},
            creator_id = #{item.creatorId},
            modifier = #{item.modifier},
            modifier_id = #{item.modifierId},
            ctime = #{item.ctime},
            mtime = #{item.mtime}
            WHERE order_no = #{item.orderNo}
        </foreach>
    </update>

    <update id="batchUpdateVehicleOrder">
        <foreach collection="list" item="item" separator=";">
            UPDATE dd_vehicle_order
            SET
            passenger_id = #{item.passengerId},
            passenger_employee_no = #{item.passengerEmployeeNo},
            passenger_name = #{item.passengerName},
            passenger_dept_id = #{item.passengerDeptId},
            passenger_dept_name = #{item.passengerDeptName},
            travel_time = #{item.travelTime},
            arrival_time = #{item.arrivalTime},
            vehicle_type = #{item.vehicleType},
            departure_city = #{item.departureCity},
            departure_address = #{item.departureAddress},
            arrival_city = #{item.arrivalCity},
            arrival_address = #{item.arrivalAddress},
            travel_distance = #{item.travelDistance},
            company_actual_payment = #{item.companyActualPayment},
            service_fee = #{item.serviceFee},
            payment_type = #{item.paymentType},
            booking_date = #{item.bookingDate},
            booking_user_id = #{item.bookingUserId},
            booking_employee_no = #{item.bookingEmployeeNo},
            booking_employee_name = #{item.bookingEmployeeName},
            booking_dept_id = #{item.bookingDeptId},
            booking_dept_name = #{item.bookingDeptName},
            business_trip_application_no = #{item.businessTripApplicationNo},
            business_trip_reason = #{item.businessTripReason},
            cost_center_id = #{item.costCenterId},
            cost_center_name = #{item.costCenterName},
            project_id = #{item.projectId},
            project_name = #{item.projectName},
            project_code = #{item.projectCode},
            company_id = #{item.companyId},
            company_name = #{item.companyName},
            accounting_period = #{item.accountingPeriod},
            expense_report_no = #{item.expenseReportNo},
            initiation_status = #{item.initiationStatus},
            initiation_date = #{item.initiationDate},
            request_id = #{item.requestId},
            creator = #{item.creator},
            creator_id = #{item.creatorId},
            modifier = #{item.modifier},
            modifier_id = #{item.modifierId},
            ctime = #{item.ctime},
            mtime = #{item.mtime}
            WHERE order_no = #{item.orderNo}
        </foreach>
    </update>

    <select id="getFlightTicketOrders" resultType="com.gok.pboot.pms.entity.DdFlightTicketOrder">
        SELECT * FROM dd_flight_ticket_order
        WHERE order_no IN
        <foreach collection="orderNoSet" item="orderNo" open="(" separator="," close=")">
            #{orderNo}
        </foreach>
    </select>

    <select id="getHotelOrders" resultType="com.gok.pboot.pms.entity.DdHotelOrder">
        SELECT * FROM dd_hotel_order
        WHERE order_no IN
        <foreach collection="orderNoSet" item="orderNo" open="(" separator="," close=")">
            #{orderNo}
        </foreach>
    </select>

    <select id="getVehicleOrders" resultType="com.gok.pboot.pms.entity.DdVehicleOrder">
        SELECT * FROM dd_vehicle_order
        WHERE order_no IN
        <foreach collection="orderNoSet" item="orderNo" open="(" separator="," close=")">
            #{orderNo}
        </foreach>
    </select>

    <select id="flightTicketOrderStatistics" resultType="java.util.Map">
        select
        sum(o.company_actual_payment) as companyActualPaymentTotal,
        sum(ifnull(o.service_fee, 0)) as serviceFeeTotal
        from dd_flight_ticket_order o
        <include refid="Common_Where_Condition"/>
    </select>

    <select id="hotelOrderStatistics" resultType="java.util.Map">
        select
        sum(o.company_actual_payment) as companyActualPaymentTotal,
        sum(ifnull(o.service_fee, 0)) as serviceFeeTotal
        from dd_flight_ticket_order o
        <include refid="Common_Where_Condition"/>
    </select>

    <select id="vehicleOrderStatistics" resultType="java.util.Map">
        select
        sum(o.company_actual_payment) as companyActualPaymentTotal,
        sum(ifnull(o.service_fee, 0)) as serviceFeeTotal
        from dd_flight_ticket_order o
        <include refid="Common_Where_Condition"/>
    </select>
</mapper>