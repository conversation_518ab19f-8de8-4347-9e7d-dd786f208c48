package com.gok.pboot.pms.didi.controller;

import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.didi.service.DidiSyncService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 滴滴项目同步控制器
 *
 * <AUTHOR>
 * @date 2025/01/27
 */
@Slf4j
@RestController
@RequestMapping("/didi")
@Api(tags = "滴滴项目同步")
@AllArgsConstructor
public class DidiSyncController {

    private final DidiSyncService didiSyncService;



    @PostMapping("/project/batch-sync")
    @ApiOperation("批量同步项目信息至滴滴")
    public ApiResult<String> batchSyncProjects() {
        didiSyncService.batchSyncProjects();
        return ApiResult.successMsg("批量同步项目信息成功");
    }
    @PostMapping("/batch-sync")
    @ApiOperation("批量同步用户信息至滴滴")
    public ApiResult<String> batchSyncUsers() {
        try {
            didiSyncService.batchSyncUsers();
            return ApiResult.successMsg("批量同步用户信息成功");
        } catch (Exception e) {
            log.error("批量同步用户信息失败", e);
            return ApiResult.failure("批量同步用户信息失败: " + e.getMessage());
        }
    }

} 