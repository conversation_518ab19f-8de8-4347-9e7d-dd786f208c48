package com.gok.pboot.pms.didi.interceptor;

import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestBody;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import com.dtflys.forest.reflection.ForestMethod;
import com.gok.pboot.pms.didi.entity.dto.DidiAuthReq;
import com.gok.pboot.pms.didi.service.DidiSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 滴滴-请求-拦截器
 *
 * <AUTHOR>
 * @date 2025/08/04
 */
@Component
@Slf4j
public class DidiInterceptor implements Interceptor<Object> {
    @Resource
    private ApplicationContext applicationContext;

    private static final String ACCESS_TOKEN = "access_token";

    private static final String CLIENT_ID = "client_id";

    private static final String TIMESTAMP = "timestamp";

    private static final String SIGN = "sign";

    @Override
    public void onInvokeMethod(ForestRequest request, ForestMethod method, Object[] args) {
        log.info("Request invoke: {}", request);
    }

    @Override
    public boolean beforeExecute(ForestRequest request) {
        DidiSyncService didiSyncService = applicationContext.getBean(DidiSyncService.class);
        DidiAuthReq didiAuthReq = didiSyncService.getDidiAuthReq();
        ForestBody body = request.getBody();
        body.setNameValue(ACCESS_TOKEN, didiSyncService.getAccessToken());
        body.setNameValue(CLIENT_ID, didiAuthReq.getClient_id());
        body.setNameValue(TIMESTAMP, didiAuthReq.getTimestamp());
        body.setNameValue(SIGN, didiAuthReq.getSign());
        return true;
    }

    @Override
    public void onError(ForestRuntimeException ex, ForestRequest request, ForestResponse response) {
        log.error(
                "滴滴接口请求错误: statusCode={}, content={}, result={}",
                response.getStatusCode(),
                response.getContent(),
                response.getResult()
        );
    }

    @Override
    public void afterExecute(ForestRequest request, ForestResponse response) {

    }
}
