package com.gok.pboot.pms.didi.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.pms.didi.client.DidiClient;
import com.gok.pboot.pms.didi.entity.dto.*;
import com.gok.pboot.pms.didi.service.DidiSyncService;
import com.gok.pboot.pms.didi.util.DidiSignUtil;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.domain.Roster;
import com.gok.pboot.pms.service.IDdProjectRelationService;
import com.gok.pboot.pms.service.IProjectInfoService;
import com.gok.pboot.pms.service.RosterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 滴滴项目同步服务实现类
 *
 * <AUTHOR>
 * @date 2025/01/27
 */
@Slf4j
@Service
public class DidiSyncServiceImpl implements DidiSyncService {

    @Resource
    private DidiClient didiClient;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private RosterService rosterService;

    @Resource
    private IProjectInfoService projectInfoService;

    @Resource
    private IDdProjectRelationService ddProjectRelationService;

    @Value("${didi.sync.user.interval:200}")
    private Long syncInterval;

    @Value("${didi.sync.project.manager:true}")
    private Boolean syncManager;

    @Value("${didi.client.id:1}")
    public String clientId;

    @Value("${didi.client.secret:1}")
    public String clientSecret;

    @Value("${didi.client.grant.type:client_credentials}")
    private String grantType;



    private static final String DIDI_ACCESS_TOKEN_KEY = "didi:access_token";

    private static final String DIDI_ACCESS_AUTH_REQ_KEY = "didi:auth_req";


    public synchronized boolean syncProjectToDidi(ProjectInfo projectInfo) {
        try {
            // 转换项目信息
            DidiProjectSyncReq syncReq = convertToDidiProjectSyncReq(projectInfo);

            // 调用滴滴API同步项目
            DidiResult<DidiProjectSyncRes> result = didiClient.addProject(syncReq);

            if (result != null && result.getErrno() == 0) {
                log.info("项目同步成功: {}", projectInfo.getItemNo());

                // 保存或更新项目与滴滴项目的关联关系
                if (result.getData() != null && StrUtil.isNotBlank(result.getData().getProject_id())) {
                    boolean relationSaved = ddProjectRelationService.saveOrUpdateRelation(
                            projectInfo.getId(),
                            projectInfo.getItemNo(),
                            result.getData().getProject_id()
                    );
                    if (relationSaved) {
                        log.info("项目关联关系保存成功: 项目ID={}, 项目编码={}, 滴滴项目ID={}",
                                projectInfo.getId(), projectInfo.getItemNo(), result.getData().getProject_id());
                    } else {
                        log.warn("项目关联关系保存失败: 项目ID={}, 项目编码={}, 滴滴项目ID={}",
                                projectInfo.getId(), projectInfo.getItemNo(), result.getData().getProject_id());
                    }
                } else {
                    log.warn("滴滴API返回的项目ID为空，无法保存关联关系: {}", projectInfo.getItemNo());
                }

                return true;
            } else {
                log.error("项目同步失败: {}, 错误信息: {}", projectInfo.getItemNo(),
                        result != null ? result.getErrmsg() : "未知错误");
                return false;
            }
        } catch (Exception e) {
            log.error("项目同步异常: {}", projectInfo.getItemNo(), e);
            return false;
        }
    }


    @Override
    public void batchSyncProjects() {
        List<ProjectInfo> projectInfoList = projectInfoService.list();
        for (ProjectInfo projectInfo : projectInfoList) {
            boolean success = syncProjectToDidi(projectInfo);
            if (!success) {
                log.error("批量同步中项目同步失败: {}", projectInfo.getItemNo());
            }
        }

        log.info("批量同步完成，总数: {}, 成功: {}", projectInfoList.size(),
                projectInfoList.stream().mapToInt(p -> syncProjectToDidi(p) ? 1 : 0).sum());

    }

    public boolean updateProjectToDidi(ProjectInfo projectInfo) {
        try {
            // 转换项目信息
            DidiProjectSyncReq syncReq = convertToDidiProjectSyncReq(projectInfo);

            // 调用滴滴API更新项目
            DidiResult<DidiProjectSyncRes> result = didiClient.updateProject(syncReq);

            if (result != null && result.getErrno() == 0) {
                log.info("项目更新成功: {}", projectInfo.getItemNo());

                // 更新同步时间
                boolean timeUpdated = ddProjectRelationService.updateSyncTime(projectInfo.getId());
                if (timeUpdated) {
                    log.info("项目同步时间更新成功: 项目ID={}, 项目编码={}",
                            projectInfo.getId(), projectInfo.getItemNo());
                } else {
                    log.warn("项目同步时间更新失败: 项目ID={}, 项目编码={}",
                            projectInfo.getId(), projectInfo.getItemNo());
                }

                return true;
            } else {
                log.error("项目更新失败: {}, 错误信息: {}", projectInfo.getItemNo(),
                        result != null ? result.getErrmsg() : "未知错误");
                return false;
            }
        } catch (Exception e) {
            log.error("项目更新异常: {}", projectInfo.getItemNo(), e);
            return false;
        }
    }

    @Override
    public DidiAuthReq getDidiAuthReq() {
        String str = stringRedisTemplate.opsForValue().get(DIDI_ACCESS_AUTH_REQ_KEY);
        if (str == null) {
            return null;
        }
        return JSONObject.parseObject(str, DidiAuthReq.class);
    }


    public boolean syncUserToDidi(Roster roster) {
        try {
            log.info("开始同步用户至滴滴: {}", roster.getWorkCode());

            // 转换用户信息
            DidiUserSyncReq syncReq = convertToDidiUserSyncReq(roster);

            // 调用滴滴API同步用户
            Map<String, Object> reqMap = new HashMap<>(5);
            reqMap.put("data", syncReq);
            DidiResult<DidiUserSyncRes> result = didiClient.addUser(reqMap);

            if (result != null && result.getErrno() == 0) {
                log.info("用户同步成功: {}, 滴滴用户ID: {}", roster.getWorkCode(),
                        result.getData() != null ? result.getData().getId() : "未返回");
                return true;
            } else {
                log.error("用户同步失败: {}, 错误信息: {}", roster.getWorkCode(),
                        result != null ? result.getErrmsg() : "未知错误");
                return false;
            }
        } catch (Exception e) {
            log.error("用户同步异常: {}", roster.getWorkCode(), e);
            return false;
        }
    }

    @Override
    public synchronized void batchSyncUsers() {
        List<Roster> rosterList = rosterService.list();
        int successCount = 0;
        int failCount = 0;

        log.info("开始批量同步用户，总数: {}", rosterList.size());

        for (Roster roster : rosterList) {
            try {
                boolean success = syncUserToDidi(roster);
                if (success) {
                    successCount++;
                } else {
                    failCount++;
                }

                // 添加间隔，避免请求过于频繁（滴滴要求间隔150ms以上）
                if (syncInterval > 0) {
                    Thread.sleep(syncInterval);
                }
            } catch (InterruptedException e) {
                log.warn("同步间隔被中断: {}", roster.getWorkCode());
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error("批量同步中用户同步异常: {}", roster.getWorkCode(), e);
                failCount++;
            }
        }

        log.info("批量同步完成，总数: {}, 成功: {}, 失败: {}", rosterList.size(), successCount, failCount);
    }


    /**
     * 将Roster信息转换为滴滴用户同步请求
     */
    private DidiUserSyncReq convertToDidiUserSyncReq(Roster roster) {
        DidiUserSyncReq syncReq = new DidiUserSyncReq();

        // 设置基本认证信息（这些会在拦截器中设置）
        syncReq.setClient_id(clientId);
        syncReq.setAccess_token(getAccessToken());
        syncReq.setTimestamp((int) (System.currentTimeMillis() / 1000));

        // 构建用户数据
        DidiUserSyncReq.UserData userData = new DidiUserSyncReq.UserData();

        // 基本信息
        userData.setRealname(roster.getAliasName());
        userData.setEmployee_number(roster.getWorkCode());
        // 使用工号作为唯一标识
        userData.setMember_type(1);

        // 部门信息
        if (roster.getDeptId() != null) {
            userData.setBudget_center_id(roster.getDeptId());
        }

        // 职位信息
        if (StrUtil.isNotBlank(roster.getJob())) {
            userData.setNickname(roster.getJob());
        }

        // 上级信息
        if (roster.getLeaderId() != null) {
            Roster leader = rosterService.getById(roster.getLeaderId());
            if (leader != null && StrUtil.isNotBlank(leader.getWorkCode())) {
                userData.setImmediate_superior_employee_number(leader.getWorkCode());
            }
        }

        // 默认配置
        // 车辆预定人员
        userData.setSystem_role(0);
        // 企业支付
        userData.setUse_company_money(1);
        // 不限额度
        userData.setTotal_quota("0");
        // 备注选填
        userData.setIs_remark("0");

        // 将userData转换为JSON字符串
        syncReq.setData(JSONObject.toJSONString(userData));

        return syncReq;
    }

    /**
     * 获取滴滴访问令牌
     */
    @Override
    public synchronized String getAccessToken() {
        try {
            String accessToken = stringRedisTemplate.opsForValue().get(DIDI_ACCESS_TOKEN_KEY);
            if (StringUtils.isNotBlank(accessToken)) {
                return accessToken;
            }
            DidiAuthReq authReq = new DidiAuthReq();
            authReq.setClient_id(clientId);
            authReq.setClient_secret(clientSecret);
            authReq.setGrant_type(grantType);
            authReq.setTimestamp((int) (System.currentTimeMillis() / 1000));
            // 这里需要根据滴滴的签名算法生成签名
            authReq.setSign(generateSign(authReq));

            DidiAuthRes authRes = didiClient.getToken(authReq);
            if (authRes != null && authRes.getAccess_token() != null) {
                stringRedisTemplate.opsForValue().set(DIDI_ACCESS_TOKEN_KEY, authRes.getAccess_token(), 25, TimeUnit.MINUTES);
                stringRedisTemplate.opsForValue().set(DIDI_ACCESS_AUTH_REQ_KEY, JSONObject.toJSONString(authReq), 25, TimeUnit.MINUTES);
                return authRes.getAccess_token();
            }
        } catch (Exception e) {
            log.error("获取滴滴访问令牌异常", e);
            stringRedisTemplate.delete(DIDI_ACCESS_TOKEN_KEY);
            stringRedisTemplate.delete(DIDI_ACCESS_AUTH_REQ_KEY);
            throw new BusinessException("获取滴滴访问令牌异常");
        }
        return null;
    }

    /**
     * 生成签名
     * 注意：这里需要根据滴滴的具体签名算法实现
     */
    private String generateSign(DidiAuthReq authReq) {
        return DidiSignUtil.generateSign(authReq);
    }

    /**
     * 将项目信息转换为滴滴项目同步请求
     */
    private DidiProjectSyncReq convertToDidiProjectSyncReq(ProjectInfo projectInfo) {
        Long managerUserId = projectInfo.getManagerUserId();
        String workCode = null;
        if (managerUserId != null && syncManager) {
            Roster roster = rosterService.getById(managerUserId);
            workCode = roster.getWorkCode();
        }
        //同步【项目名称】、【项目编号】、【项目经理】
        return new DidiProjectSyncReq()
                .setType(1)
                .setName(projectInfo.getItemName())
                .setOut_budget_id(projectInfo.getItemNo())
                .setLeader_employee_id(workCode)
                ;
    }
} 