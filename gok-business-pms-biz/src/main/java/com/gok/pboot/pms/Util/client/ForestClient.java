package com.gok.pboot.pms.Util.client;

import com.alibaba.fastjson.JSONObject;
import com.dtflys.forest.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * forest 请求第三方接口
 *
 * <AUTHOR>
 */
public interface ForestClient {

    /**
     * 获取DbApitoken
     *
     * @param url
     * @param appId
     * @param secret
     * @return
     */
    @Get(url = "${url}/token/generate?appid=${appId}&secret=${secret}")
    JSONObject getDbApiToken(@Var("url") String url, @Var("appId") String appId, @Var("secret") String secret);

    /**
     * 获取合同明细
     */
    @Post(url = "${url}${api}", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject contractDetails(@Var("url") String url, @Var("api") String api, @Var("token") String token,
                               @Body("contractType") String contractType, @Body("projectId") String projectId,
                               @Body("searchstarttime") String searchstarttime, @Body("searchendtime") String searchendtime);

    /**
     * 项目管理导出-获取合同数据
     */
    @Post(url = "${url}${api}", headers = {"Content-Type:application/json",
            "Authorization:${token}"})
    JSONObject exportContractDetails(@Var("url") String url, @Var("api") String api, @Var("token") String token,
                                     @JSONBody Map<String, Object> filter);

    /**
     * 项目管理-工时人天明细
     *
     * @param url
     * @param api
     * @param token
     * @param projectId
     * @param userStatus
     * @param workType
     * @param searchstarttime
     * @param searchendtime
     * @return
     */
    @Post(url = "${url}${api}", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject manDays(@Var("url") String url, @Var("api") String api, @Var("token") String token, @Body("projectId") Long projectId,
                       @Body("userStatus") String userStatus, @Body("workType") String workType,
                       @Body("searchstarttime") String searchstarttime, @Body("searchendtime") String searchendtime);

    /**
     * 项目管理-项目采购付款明细
     */
    @Post(url = "${url}${api}", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject procureDetails(@Var("url") String url, @Var("api") String api, @Var("token") String token,
                              @Body("projectId") String projectId, @Body("searchstarttime") String searchstarttime, @Body("searchendtime") String searchendtime);

    /**
     * 项目管理-字典查询
     */
    @Post(url = "${url}${api}", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject projectDict(@Var("url") String url, @Var("api") String api, @Var("token") String token,
                           @Body("fieldid") String fieldid);

    /**
     * 项目管理-项目费用项科目对照代表详情
     */
    @Post(url = "${url}${api}", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject subjectDetails(@Var("url") String url, @Var("api") String api, @Var("token") String token);


    /**
     * 项目管理-项目费用报销明细
     */
    @Post(url = "${url}${api}", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject reimburseDetails(@Var("url") String url, @Var("api") String api, @Var("token") String token,
                                @Body("projectId") String projectId,
                                @Body("searchstarttime") String searchstarttime,
                                @Body("searchendtime") String searchendtime,
                                @Body("fyxlb") String fyxlb);

    /**
     * 项目管理-项目费用报销明细
     */
    @Post(url = "${url}${api}", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject advanceDetails(@Var("url") String url, @Var("api") String api, @Var("token") String token,
                              @Body("projectId") String projectId,
                              @Body("searchstarttime") String searchstarttime,
                              @Body("searchendtime") String searchendtime);

    /**
     * 项目管理-直接人工（项目分摊）明细
     */
    @Post(url = "${url}${api}", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject totalCostDetails(@Var("url") String url, @Var("api") String api, @Var("token") String token,
                                @Body("projectName") String projectName,
                                @Body("searchstarttime") String searchstarttime,
                                @Body("searchendtime") String searchendtime);

    /**
     * 项目管理-预估外部采购明细
     */
    @Post(url = "${url}${api}", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject externalProcureDetails(@Var("url") String url, @Var("api") String api, @Var("token") String token,
                                      @Body("projectId") String projectId,
                                      @Body("searchstarttime") String searchstarttime,
                                      @Body("searchendtime") String searchendtime);

    /**
     * 项目详情-合同数据
     *
     * @param url          url
     * @param api          api
     * @param token        令牌
     * @param projectId    项目id
     * @param contractType 合同类型
     * @return {@link JSONObject}
     */
    @Post(url = "${url}${api}", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject projectContract(@Var("url") String url, @Var("api") String api, @Var("token") String token,
                               @Body("projectId") String projectId, @Body("contractType") String contractType);

    /**
     * 项目详情-财务数据
     *
     * @param url       url
     * @param api       api
     * @param token     令牌
     * @param projectId 项目id
     * @return {@link JSONObject}
     */
    @Post(url = "${url}${api}", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject financialData(@Var("url") String url, @Var("api") String api, @Var("token") String token,
                             @Body("projectId") String projectId,
                             @Body("searchstarttime") String searchstarttime,
                             @Body("searchendtime") String searchendtime);

    /**
     * 财务数据-追加预算总收入
     *
     * @param url       url
     * @param api       api
     * @param token     令牌
     * @param projectId 项目id
     * @return {@link JSONObject}
     */
    @Post(url = "${url}${api}", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject additionalIncome(@Var("url") String url, @Var("api") String api, @Var("token") String token,
                                @Body("projectId") String projectId,
                                @Body("searchstarttime") String searchstarttime,
                                @Body("searchendtime") String searchendtime);

    /**
     * 财务数据-已有预算总收入
     *
     * @param url       url
     * @param api       api
     * @param token     令牌
     * @param projectId 项目id
     * @return {@link JSONObject}
     */
    @Post(url = "${url}${api}", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject existingIncome(@Var("url") String url, @Var("api") String api, @Var("token") String token,
                              @Body("projectId") String projectId,
                              @Body("searchstarttime") String searchstarttime,
                              @Body("searchendtime") String searchendtime);

    /**
     * 财务数据-追加预算总成本
     *
     * @param url       url
     * @param api       api
     * @param token     令牌
     * @param projectId 项目id
     * @return {@link JSONObject}
     */
    @Post(url = "${url}${api}", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject additionalCosts(@Var("url") String url, @Var("api") String api, @Var("token") String token,
                               @Body("projectId") String projectId,
                               @Body("searchstarttime") String searchstarttime,
                               @Body("searchendtime") String searchendtime);

    /**
     * 财务数据-已有预算总成本
     *
     * @param url       url
     * @param api       api
     * @param token     令牌
     * @param projectId 项目id
     * @return {@link JSONObject}
     */
    @Post(url = "${url}${api}", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject existingBudget(@Var("url") String url, @Var("api") String api, @Var("token") String token,
                              @Body("projectId") String projectId,
                              @Body("searchstarttime") String searchstarttime,
                              @Body("searchendtime") String searchendtime);

    /**
     * 修改OA项目成员
     *
     * @param url       url
     * @param api       api
     * @param token     令牌
     * @param projectId 项目id
     * @param xmcy      项目成员
     * @return {@link JSONObject}
     */
    @Post(url = "${url}${api}", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject updateProjectXmcy(@Var("url") String url, @Var("api") String api, @Var("token") String token,
                                 @Body("projectId") String projectId,
                                 @Body("xmcy") String xmcy);

    /**
     * 获取oa存在账号用户id
     *
     * @param url     url
     * @param api     api
     * @param token   令牌
     * @param userIds 用户id列表
     * @return {@link JSONObject}
     */
    @Post(url = "${url}${api}", headers = {"Content-Type:application/json",
            "Authorization:${token}"})
    JSONObject batchExistsOaUserId(@Var("url") String url, @Var("api") String api, @Var("token") String token,
                                   @Body("userIds") List<String> userIds);

    /**
     * 获取oa存在账号用户id
     *
     * @param url     url
     * @param api     api
     * @param token   令牌
     * @param userIds 用户id列表
     * @return {@link JSONObject}
     */
    @Post(url = "${url}${api}", headers = {"Content-Type:application/json",
            "Authorization:${token}"})
    JSONObject getExistsOaUserIds(@Var("url") String url, @Var("api") String api, @Var("token") String token,
                                  @Body("userIds") List<String> userIds);

    /**
     * 客户台帐
     *
     * @param url          路径
     * @param token        token
     * @param customerName 客户名称
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/customerAccount", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject customerAccount(@Var("url") String url, @Var("token") String token,
                               @Body("customerName") String customerName);

    /**
     * 银行台帐
     *
     * @param url   路径
     * @param token token
     * @param name  银行名称
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/bankAccount", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject bankAccount(@Var("url") String url, @Var("token") String token,
                           @Body("name") String name);

    /**
     * 根据合同编码搜索归属合同收款明细
     *
     * @param url   路径
     * @param token token
     * @param id    id
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/contractPayment", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject contractPayment(@Var("url") String url, @Var("token") String token,
                               @Body("id") String id);

    /**
     * 根据合同名称搜索归属合同收款明细
     *
     * @param url          路径
     * @param token        token
     * @param contractName 合同名称
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/contractName", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject contractName(@Var("url") String url, @Var("token") String token,
                            @Body("contractName") String contractName);

    /**
     * 归属合同收款明细
     *
     * @param url            路径
     * @param token          token
     * @param contractNumber 合同单号
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/contractPaymentByNumber", headers = {"Content-Type:application/json", "Authorization:${token}"})
    JSONObject contractPaymentByNumber(@Var("url") String url, @Var("token") String token,
                                       @Body("contractNumber") List<String> contractNumber);

    /**
     * 项目台帐
     *
     * @param url         路径
     * @param token       token
     * @param projectName 项目名称
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/projectAccount", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject projectAccount(@Var("url") String url, @Var("token") String token,
                              @Body("projectName") String projectName);

    /**
     * 项目台帐
     *
     * @param url         路径
     * @param token       token
     * @param projectName 项目名称
     * @param projectId 项目Id
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/projectAccountByName", headers = {"Content-Type:application/json", "Authorization:${token}"})
    JSONObject projectAccountByName(@Var("url") String url, @Var("token") String token,
                                    @Body("projectName") List<String> projectName, @Body("projectId") List<Long> projectId);

    /**
     * 合同台账验收报告
     *
     * @param url
     * @param readToken
     * @param contractId
     * @return
     */
    @Post(url = "${url}/api/project/acceptance/records", headers = {"Content-Type:application/json", "Authorization:${token}"})
    JSONObject getAcceptanceRecordsUrl(@Var("url") String url, @Var("token") String readToken, @Body("id") Long contractId);

    /**
     * 批量查询合同台账验收报告
     *
     * @param url
     * @param readToken
     * @param ids
     * @return
     */
    @Post(url = "${url}/api/project/acceptance/recordsByIds", headers = {"Content-Type:application/json", "Authorization:${token}"})
    JSONObject getAcceptanceRecordsUrlByIds(@Var("url") String url, @Var("token") String readToken, @Body("ids") List<Long> ids);

    /**
     * 合同台账风险记录
     *
     * @param url
     * @param readToken
     * @param contractId
     * @return
     */
    @Post(url = "${url}/api/project/contract/risk", headers = {"Content-Type:application/json", "Authorization:${token}"})
    JSONObject getContractRiskUrl(@Var("url") String url, @Var("token") String readToken, @Body("id") Long contractId);

    /**
     * 批量合同台账风险记录
     *
     * @param url
     * @param readToken
     * @param ids
     * @return
     */
    @Post(url = "${url}/api/project/contract/riskByIds", headers = {"Content-Type:application/json", "Authorization:${token}"})
    JSONObject getContractRiskUrlByIds(@Var("url") String url, @Var("token") String readToken, @Body("ids") List<Long> ids);

    /**
     * 获取OA账号
     */
    @Post(url = "${url}/api/getOaAccountById", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject getOaAccountById(@Var("url") String url, @Var("token") String token, @Body("id") Long id);

    /**
     * 获取OA账号
     */
    @Post(url = "${url}/api/getBusinessCaseIdByProjectId", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject getBusinessCaseIdByProjectId(@Var("url") String url, @Var("token") String token, @Body("id") Long id);


    /**
     * 插入OA流程字典
     */
    @Post(url = "${url}/api/insertWorkflowSelectitem", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject insertWorkflowSelectitem(@Var("url") String url, @Var("token") String token,
                                        @Body("fieldid") String fieldid, @Body("isbill") String isbill, @Body("selectvalue") String selectvalue,
                                        @Body("selectname") String selectname, @Body("listorder") String listorder, @Body("isdefault") String isdefault, @Body("doccategory") String doccategory,
                                        @Body("isaccordtosubcom") String isaccordtosubcom, @Body("cancel") String cancel, @Body("pubid") String pubid, @Body("uuid") String uuid);


    /**
     * 更新OA流程字典
     */
    @Post(url = "${url}/api/updateWorkflowSelectitem", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject updateWorkflowSelectitem(@Var("url") String url, @Var("token") String token,
                                        @Body("selectname") String selectname, @Body("cancel") String cancel, @Body("pubid") String pubid, @Body("uuid") String uuid);

    /**
     * 查询OA流程字典最大值
     */
    @Post(url = "${url}/api/getWorkflowSelectitemMax", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject getWorkflowSelectitemMax(@Var("url") String url, @Var("token") String token, @Body("fieldid") String fieldid);

    /**
     * 插入OA公共字典
     */
    @Post(url = "${url}/api/insertModeSelectitempagedetail", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject insertModeSelectitempagedetail(@Var("url") String url, @Var("token") String token,
                                              @Body("mainid") String mainid, @Body("name") String name, @Body("disorder") String disorder,
                                              @Body("maincategory") String maincategory, @Body("isaccordtosubcom") String isaccordtosubcom,
                                              @Body("pid") String pid, @Body("statelev") String statelev, @Body("cancel") String cancel, @Body("uuid") String uuid);

    /**
     * 更新OA公共字典
     */
    @Post(url = "${url}/api/updateModeSelectitempagedetail", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject updateModeSelectitempagedetail(@Var("url") String url, @Var("token") String token,
                                              @Body("name") String name, @Body("cancel") String cancel, @Body("pid") String pid, @Body("uuid") String uuid);


    /**
     * 查询OA公共字典
     */
    @Post(url = "${url}/api/getModeSelectitempagedetail", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject getModeSelectitempagedetail(@Var("url") String url, @Var("token") String token, @Body("uuid") String uuid);


    /**
     * 查询OA公共字典最大值
     */
    @Post(url = "${url}/api/getModeSelectitempagedetailMax", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject getModeSelectitempagedetailMax(@Var("url") String url, @Var("token") String token, @Body("mainid") String mainid, @Body("statelev") String statelev);

    /**
     * 获取成本科目配置信息列表
     *
     * @param url   url
     * @param api   api
     * @param token token
     * @return data
     */
    @Post(url = "${url}${api}", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject getCostSubjectConfigInfoList(@Var("url") String url, @Var("api") String api, @Var("token") String token);

    /**
     * 查询OA流程状态
     *
     * @param url        网址
     * @param token      令 牌
     * @param requestIds 请求 ID
     * @return {@link JSONObject }
     */
    @Post(url = "${url}/api/getOARequestStatus", headers = {"Content-Type:application/json",
            "Authorization:${token}"})
    JSONObject getOARequestStatus(@Var("url") String url, @Var("token") String token, @Body("requestIds") List<Long> requestIds);

    /**
     * 同步OA项目台账最新目标内容
     *
     * @param url   url
     * @param api   api
     * @param token token
     * @return data
     */
    @Post(url = "${url}${api}", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject getProjectTargetInfo(@Var("url") String url, @Var("api") String api, @Var("token") String token);

    /**
     * 同步OA项目预算台账最新成本管理信息内容
     *
     * @param url   url
     * @param api   api
     * @param token token
     * @return data
     */
    @Post(url = "${url}${api}", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject getProjectBudgetInfo(@Var("url") String url, @Var("api") String api, @Var("token") String token, @Body("start") Long start, @Body("end") Long end);

    /**
     * 查询OA流程项目商业论证(A表)
     */
    @Post(url = "${url}/api/getOARequestByA", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject getOARequestByA(@Var("url") String url, @Var("token") String token, @Body("projectId") Long projectId);

    /**
     * 查询OA流程项目立项申请(B表)
     */
    @Post(url = "${url}/api/getOARequestByB", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject getOARequestByB(@Var("url") String url, @Var("token") String token, @Body("projectId") Long projectId);

    /**
     * 查询OA流程项目变更
     */
    @Post(url = "${url}/api/getOARequestByXMBG", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject getOARequestByXMBG(@Var("url") String url, @Var("token") String token, @Body("projectId") Long projectId);

    /**
     * 查询oa流程是否被删除
     *
     * @param url        网址
     * @param token      令 牌
     * @param requestIds 请求 ID
     * @return {@link JSONObject }
     */
    @Post(url = "${url}/api/getOARequest", headers = {"Content-Type:application/json",
            "Authorization:${token}"})
    JSONObject getOARequest(@Var("url") String url, @Var("token") String token, @Body("requestIds") Set<Long> requestIds);

    /**
     * 查询人力外包结算单审批表明细
     *
     * @param url
     * @param token
     * @param requestId
     * @return
     */
    @Post(url = "${url}/api/getSettlementDetail", headers = {"Content-Type:application/json",
            "Authorization:${token}"})
    JSONObject getSettlementDetail(@Var("url") String url, @Var("token") String token, @Body("requestId") Long requestId);


    /**
     * 通过OA流程名称查询流程workflowId
     *
     * @param url
     * @param token
     * @param workflowName
     * @return
     */
    @Post(url = "${url}/api/getWorkflowIdByName", headers = {"Content-Type:application/json",
            "Authorization:${token}"})
    JSONObject getWorkflowIdByName(@Var("url") String url, @Var("token") String token, @Body("workflowName") String workflowName);


    /**
     * 获取项目预算台账信息
     *
     * @return {@link JSONObject }
     */
    @Get(url = "${url}/api/getInfoByXmysb?projectId=${projectId}&projectIds=${projectIds}", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject getInfoByXmysb(@Var("url") String url, @Var("token") String token, @Var("projectId") Long projectId, @Var("projectIds") List<Long> projectIds);

    /**
     * 获取部门月预算
     *
     * @return {@link JSONObject }
     */
    @Get(url = "${url}/api/getInfoByBmyysb", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject getInfoByBmyysb(@Var("url") String url, @Var("token") String token);


    /**
     * 获取OA部门
     *
     * @return {@link JSONObject }
     */
    @Get(url = "${url}/api/getInfoByDept", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject getInfoByDept(@Var("url") String url, @Var("token") String token);

    /**
     * 获取OA现金流明细
     *
     * @param url       网址
     * @param token     令 牌
     * @param projectId 项目 ID
     * @return {@link JSONObject }
     */
    @Get(url = "${url}/api/getOaCashFlowPlanDt?projectId=${projectId}",
            headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject getOaCashFlowPlanDt(@Var("url") String url, @Var("token") String token, @Var("projectId") Long projectId);

    /**
     * 获取项目变更流程信息
     *
     * @param url   网址
     * @param token 令 牌
     * @return {@link JSONObject }
     */
    @Post(url = "${url}/api/getXMBGByRequestIds",
            headers = {"Content-Type:application/json", "Authorization:${token}"})
    JSONObject getXMBGByRequestIds(@Var("url") String url, @Var("token") String token, @Body("requestIds") List<Long> requestIds);

}
