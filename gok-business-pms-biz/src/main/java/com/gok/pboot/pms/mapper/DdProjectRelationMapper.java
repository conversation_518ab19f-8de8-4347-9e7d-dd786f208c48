package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.domain.DdProjectRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 项目与滴滴项目关联表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Mapper
public interface DdProjectRelationMapper extends BaseMapper<DdProjectRelation> {

    /**
     * 根据项目ID查询关联关系
     *
     * @param projectId 项目ID
     * @return 关联关系
     */
    DdProjectRelation selectByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据项目编码查询关联关系
     *
     * @param projectNo 项目编码
     * @return 关联关系
     */
    DdProjectRelation selectByProjectNo(@Param("projectNo") String projectNo);

    /**
     * 根据滴滴项目ID查询关联关系
     *
     * @param didiProjectId 滴滴项目ID
     * @return 关联关系
     */
    DdProjectRelation selectByDidiProjectId(@Param("didiProjectId") String didiProjectId);

    /**
     * 更新同步时间
     *
     * @param projectId 项目ID
     * @return 更新行数
     */
    int updateSyncTimeByProjectId(@Param("projectId") Long projectId);

}
