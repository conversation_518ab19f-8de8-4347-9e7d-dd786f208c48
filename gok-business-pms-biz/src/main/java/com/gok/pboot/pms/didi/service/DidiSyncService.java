package com.gok.pboot.pms.didi.service;

import com.gok.pboot.pms.didi.entity.dto.DidiAuthReq;

/**
 * 滴滴项目同步服务接口
 *
 * <AUTHOR>
 * @date 2025/01/27
 */
public interface DidiSyncService {


    /**
     * 批量同步项目信息
     *
     */
    void batchSyncProjects();

    /**
     * 获取滴滴身份验证要求
     *
     * @return {@link DidiAuthReq }
     */
    DidiAuthReq getDidiAuthReq();

    /**
     * 批量同步用户
     */
    void batchSyncUsers();

    /**
     * 获取访问令牌
     *
     * @return {@link String }
     */
    String getAccessToken();


}