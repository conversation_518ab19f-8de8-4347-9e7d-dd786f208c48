package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.base.admin.dto.PaymentDTO;
import com.gok.base.admin.dto.ProjectPaymentClaimDTO;
import com.gok.base.admin.dto.ProjectPaymentDTO;
import com.gok.base.admin.feign.RemotePaymentService;
import com.gok.bcp.message.dto.BcpMessageTargetDTO;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.SourceEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.bcp.message.entity.model.MailModel;
import com.gok.bcp.message.feign.RemoteMailService;
import com.gok.bcp.upms.dto.MultiDimensionDeptDto;
import com.gok.bcp.upms.feign.RemoteOutMultiDeptService;
import com.gok.bcp.upms.feign.RemoteRoleService;
import com.gok.bcp.upms.vo.SysUserRoleDataVo;
import com.gok.components.common.util.R;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.*;
import com.gok.pboot.pms.common.constant.DictConstants;
import com.gok.pboot.pms.common.constant.FunctionConstants;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.entity.domain.ProjectPayment;
import com.gok.pboot.pms.entity.domain.ProjectPaymentClaim;
import com.gok.pboot.pms.entity.dto.PaymentDataScopeDTO;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.enumeration.*;
import com.gok.pboot.pms.mapper.ProjectPaymentClaimMapper;
import com.gok.pboot.pms.mapper.ProjectPaymentMapper;
import com.gok.pboot.pms.service.IProjectPaymentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目回款跟踪 ServiceImpl
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectPaymentServiceImpl extends ServiceImpl<ProjectPaymentMapper, ProjectPayment>
        implements IProjectPaymentService {

    /**
     * 门户id
     */
    @Value("${pushMessage.portalAppId}")
    private Long portalAppId;

    /**
     * 教育回款URL
     */
    @Value("${pushMessage.projectPaymentUrl}")
    private String projectPaymentUrl;

    private final ProjectPaymentClaimMapper projectPaymentClaimMapper;

    private final RemoteOutMultiDeptService remoteOutMultiDeptService;

    private final RemoteMailService remoteMailService;

    private final DbApiUtil dbApi;

    private final RemotePaymentService remotePaymentService;

    private final RemoteRoleService remoteRoleService;

    private final BcpLoggerUtils bcpLoggerUtils;

    /**
     * 模糊查询带分页
     *
     * @param dto {@link ProjectPaymentDTO}
     * @return {@link Page}<{@link ProjectPaymentVO}>
     */
    @Override
    public Page<ProjectPaymentVO> findPage(ProjectPaymentDTO dto) {
        Page<ProjectPaymentVO> projectPaymentVoPage = new Page<>(dto.getCurrent(), dto.getSize());
        // 1、条件封装 单据状态
        questionStatus(dto);
        // 2、【客户名称】无值且项目干系人未认领，该数据所有人可见
        List<ProjectPaymentVO> projectPaymentVOList = new ArrayList<>();
        List<ProjectPayment> projectPaymentListByAuth = baseMapper.listByAuth(dto);
        if (CollUtil.isNotEmpty(projectPaymentListByAuth)) {
            projectPaymentVOList.addAll(this.addVoToList(projectPaymentListByAuth));
        }
        // 3、分页查询
        List<ProjectPayment> projectPaymentList = baseMapper.findPage(dto, dataScope(dto));
        if (CollUtil.isNotEmpty(projectPaymentList)) {
            projectPaymentVOList.addAll(this.addVoToList(projectPaymentList));
        }
        // 4、设置数量总数
        projectPaymentVOList = projectPaymentVOList
                .stream()
                .distinct()
                .sorted(Comparator.comparing(ProjectPaymentVO::getDocumentNumber, Comparator.nullsLast(String::compareTo)).reversed())
                .collect(Collectors.toList());
        projectPaymentVoPage.setTotal(projectPaymentVOList.size());

        // 5、设置数据内容
        projectPaymentVOList = projectPaymentVOList
                .stream()
                .skip((dto.getCurrent() - 1) * dto.getSize())
                .limit(dto.getSize())
                .collect(Collectors.toList());
        projectPaymentVoPage.setRecords(projectPaymentVOList);

        return projectPaymentVoPage;
    }

    /**
     * 数据权限
     *
     * @param dto {@link ProjectPaymentDTO}
     * @return {@link PaymentDataScopeDTO}
     */
    private PaymentDataScopeDTO dataScope(ProjectPaymentDTO dto) {
        // 1、获取中台人员数据权限
        SysUserRoleDataVo userDataScope;
        if (SecurityUtils.getUser() != null) {
            userDataScope = remoteRoleService.getRoleDataDetailByUserId(dto.getClientId(),
                    SecurityUtils.getUser().getId(), dto.getMenuCode()).getData();
        } else {
            userDataScope = remoteRoleService.getRoleDataDetailByUserId(dto.getClientId(),
                    dto.getUserId(), dto.getMenuCode()).getData();
        }
        // 2、数据权限封装
        PaymentDataScopeDTO dataScopeDTO = new PaymentDataScopeDTO();
        if (Boolean.FALSE.equals(userDataScope.getIsAll())) {
            dataScopeDTO.setIsAll(Boolean.TRUE);
            dataScopeDTO.setUserIdList(userDataScope.getUserIdList());
            dataScopeDTO.setDeptIdList(userDataScope.getDeptIdList());
        }
        return dataScopeDTO;
    }

    /**
     * 锁定
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link  Boolean}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean lock(ProjectPaymentDTO projectPaymentDTO) {
        // 数字财务
        if (SecurityUtils.getUser() != null) {
            if (Boolean.FALSE.equals(remotePaymentService.lock(projectPaymentDTO).getData())) {
                throw new ServiceException("数字财务锁定/取消锁定操作异常");
            }
        }
        List<ProjectPayment> projectPaymentList = new ArrayList<>();
        // 无需查询锁定状态
        Integer lockStatus = projectPaymentDTO.getLockStatus();
        projectPaymentDTO.setLockStatus(null);
        // 单据状态
        questionStatus(projectPaymentDTO);
        List<ProjectPayment> projectPaymentPage = baseMapper.findPage(projectPaymentDTO, dataScope(projectPaymentDTO));
        if (CollUtil.isNotEmpty(projectPaymentPage)) {
            projectPaymentPage.forEach(p -> {
                ProjectPayment projectPayment = new ProjectPayment();
                BeanUtil.copyProperties(p, projectPayment, CopyOptions.create().setIgnoreNullValue(true));
                projectPayment.setLockStatus(lockStatus);
                // 如果原先是待锁定并且当前日期超过收款日期30天 则后续不再进行锁定
                if (LockStatusEnum.NO.getValue().equals(p.getLockStatus())
                        && DateUtil.difference(p.getPaymentDate()) > DateUtil.THIRTY_DAYS) {
                    projectPayment.setAutoLock(AutoLockEnum.NO.getValue());
                }
                projectPaymentList.add(projectPayment);
            });

            return this.updateBatchById(projectPaymentList);
        }
        return Boolean.TRUE;
    }

    /**
     * 导出Excel选中数据
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link List}
     */
    @Override
    public List export(ProjectPaymentDTO projectPaymentDTO) {
        List<ProjectPaymentVO> projectPaymentVOList;
        // 导出模板表
        if (CollUtil.isNotEmpty(projectPaymentDTO.getIds())) {
            List<ProjectPaymentExcelVO> projectPaymentExcelVOList = new ArrayList<>();
            if (NumberUtils.LONG_MINUS_ONE.equals(projectPaymentDTO.getIds().get(NumberUtils.INTEGER_ZERO))) {
                ProjectPaymentExcelVO projectPaymentExcelVO = ProjectPaymentExcelVO.builder()
                        .paymentDate(new SimpleDateFormat(DateUtil.SIMPLE_DATE_FORMAT_2).format(new Date()))
                        .build();
                projectPaymentExcelVOList.add(projectPaymentExcelVO);
                return projectPaymentExcelVOList;
            }
        }
        // 条件查询 单据状态
        questionStatus(projectPaymentDTO);
        //【客户名称】无值且项目干系人未认领，该数据所有人可见
        List<ProjectPayment> listByNoCustomerName = baseMapper.listByAuth(projectPaymentDTO);
        List<ProjectPayment> projectPaymentList = baseMapper.findPage(projectPaymentDTO, dataScope(projectPaymentDTO));
        // 封装展示统一返回
        projectPaymentVOList = this.addVoToList(projectPaymentList);
        projectPaymentVOList.addAll(this.addVoToList(listByNoCustomerName));
        //日志
        bcpLoggerUtils.log(FunctionConstants.PROJECT_PAYMENT_TRACKING_FORM,LogContentEnum.EXPORT_DATA,projectPaymentVOList.size());

        return projectPaymentVOList
                .stream()
                .distinct()
                .sorted(Comparator.comparing(ProjectPaymentVO::getDocumentNumber, Comparator.nullsLast(String::compareTo)).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 插入数据
     *
     * @param paymentDTO {@link PaymentDTO}
     * @return {@code int}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public int save(PaymentDTO paymentDTO) {
        ProjectPayment projectPayment = BeanUtil.copyProperties(paymentDTO, ProjectPayment.class, "paymentCompany");
        projectPayment.setCreatorId(paymentDTO.getUserId());
        projectPayment.setCreatorName(paymentDTO.getUsername());
        // 认领/锁定/推送状态
        projectPayment.setClaimStatus(ClaimStatusEnum.NO.getValue());
        projectPayment.setLockStatus(LockStatusEnum.NO.getValue());
        projectPayment.setPushStatus(PushStatusEnum.WAIT_PUSH.getValue());
        // 认领日期自动锁定
        if (DateUtil.difference(paymentDTO.getPaymentDate()) > DateUtil.THIRTY_DAYS) {
            projectPayment.setLockStatus(LockStatusEnum.YES.getValue());
            projectPayment.setAutoLock(AutoLockEnum.YES.getValue());
        }
        // 收款公司枚举
        if (CharSequenceUtil.isNotBlank(paymentDTO.getPaymentCompany())) {
            if (paymentDTO.getPaymentCompany().length() < 3) {
                projectPayment.setPaymentCompany(Integer.valueOf(paymentDTO.getPaymentCompany()));
            } else {
                projectPayment.setPaymentCompany(Integer.valueOf(EnumUtils.getValueByName(AttributableSubjectEnum.class, paymentDTO.getPaymentCompany())));
            }
        }

        return baseMapper.insert(projectPayment);
    }

    /**
     * 根据id获取详情
     *
     * @param id 项目id
     * @return {@link ProjectPaymentClaimVO}
     */
    @Override
    public ProjectPaymentClaimVO getOne(Long id) {
        ProjectPaymentClaimVO projectPaymentClaimVO = new ProjectPaymentClaimVO();
        // 获取项目回款跟踪
        ProjectPayment projectPayment = baseMapper.selectOne(Wrappers.<ProjectPayment>lambdaQuery()
                .eq(ProjectPayment::getId, id)
        );
        if (Optional.ofNullable(projectPayment).isPresent()) {
            // 获得项目回款详情
            ProjectPaymentClaim projectPaymentClaim = projectPaymentClaimMapper.selectOne(Wrappers.<ProjectPaymentClaim>lambdaQuery()
                    .eq(ProjectPaymentClaim::getProjectPaymentId, id)
            );
            BeanUtil.copyProperties(projectPaymentClaim, projectPaymentClaimVO, "id");
            // 封装详情信息与枚举
            projectPaymentClaimVO = BeanUtil.copyProperties(projectPayment, ProjectPaymentClaimVO.class);
            projectPaymentClaimVO.setPaymentAmount(MoneyUtil.getInstance().transType(projectPayment.getPaymentAmount()));
            projectPaymentClaimVO.setBudgetCollectionAmount(MoneyUtil.getInstance().transType(projectPayment.getBudgetCollectionAmount()));
            projectPaymentClaimVO.setPaymentCompanyTxt(EnumUtils.getNameByValue(AttributableSubjectEnum.class, String.valueOf(projectPayment.getPaymentCompany())));
            projectPaymentClaimVO.setCollectionWithinBudgetTxt(EnumUtils.getNameByValue(CollectionWithinBudgetEnum.class, projectPaymentClaim.getCollectionWithinBudget()));
            projectPaymentClaimVO.setBusinessLineTxt(EnumUtils.getNameByValue(BusinessLineEnum.class, projectPaymentClaim.getBusinessLine()));
        }

        return projectPaymentClaimVO;
    }

    /**
     * 根据id更新数据
     *
     * @param projectPaymentClaimDTO {@link ProjectPaymentClaimDTO}
     * @return {@link Boolean}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean update(ProjectPaymentClaimDTO projectPaymentClaimDTO) {
        // 1、获取项目回款跟踪
        ProjectPayment projectPayment = baseMapper.selectOne(Wrappers.<ProjectPayment>lambdaQuery()
                .eq(ProjectPayment::getId, projectPaymentClaimDTO.getId())
        );
        CopyOptions projectPaymentOption = CopyOptions.create()
                .setIgnoreNullValue(true)
                .setIgnoreError(true);
        BeanUtil.copyProperties(projectPaymentClaimDTO, projectPayment, projectPaymentOption);
        // 2、认领日期自动锁定
        if (DateUtil.difference(projectPaymentClaimDTO.getPaymentDate()) > DateUtil.THIRTY_DAYS
                && projectPayment.getClaimStatus().equals(ClaimStatusEnum.NO.getValue())
                && projectPayment.getAutoLock().equals(AutoLockEnum.YES.getValue())) {
            projectPayment.setLockStatus(LockStatusEnum.YES.getValue());
        }
        // 3、客户台帐对应备案人
        String customerName = projectPaymentClaimDTO.getCustomerName();
        if (CharSequenceUtil.isNotBlank(customerName)) {
            Map<String, CustomerAccountVO> customerAccountMap = dbApi.getCustomerAccount(CharSequenceUtil.EMPTY)
                    .stream()
                    .collect(Collectors.toMap(CustomerAccountVO::getCustomerName, a -> a, (a, b) -> a));
            CustomerAccountVO customerAccountVO = customerAccountMap.get(customerName);
            if (Optional.ofNullable(customerAccountVO).isPresent()) {
                projectPayment.setRecordManId(customerAccountVO.getRecordManId());
            }
        }
        // 4、财务数据
        if (SecurityUtils.getUser() != null) {
            if (Boolean.FALSE.equals( remotePaymentService.updateInner(projectPaymentClaimDTO).getData())) {
                throw new ServiceException("数字财务更新编辑操作异常");
            }
        }

        return baseMapper.updateById(projectPayment) > NumberUtils.INTEGER_ZERO;
    }

    /**
     * 删除
     *
     * @param ids {@link List}
     * @return {@link R}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public R<String> delete(List<Long> ids) {
        // 非推送成功状况才可删除
        List<ProjectPayment> projectPaymentList = baseMapper
                .selectList(Wrappers.<ProjectPayment>lambdaQuery()
                        .in(CollUtil.isNotEmpty(ids), ProjectPayment::getId, ids)
                )
                .stream()
                .filter(p -> PushStatusEnum.WAIT_PUSH.getValue().equals(p.getPushStatus())
                        || PushStatusEnum.FAIL_PUSH.getValue().equals(p.getPushStatus()))
                .collect(Collectors.toList());
        if (projectPaymentList.isEmpty()) {
            return R.failed("无数据删除");
        }
        // 项目回款认领数据
        List<Long> projectPaymentClaimIdList = projectPaymentClaimMapper
                .selectList(Wrappers.<ProjectPaymentClaim>lambdaQuery()
                        .in(CollUtil.isNotEmpty(ids), ProjectPaymentClaim::getProjectPaymentId, ids)
                )
                .stream()
                .map(ProjectPaymentClaim::getId)
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(projectPaymentClaimIdList)) {
            return baseMapper.deleteBatchIds(projectPaymentList) > NumberUtils.INTEGER_ZERO
                    && projectPaymentClaimMapper.deleteBatchIds(projectPaymentClaimIdList) > NumberUtils.INTEGER_ZERO
                    ? R.ok("删除回款跟踪数据成功") : R.failed("删除回款跟踪数据失败");
        }

        return baseMapper.deleteBatchIds(projectPaymentList) > NumberUtils.INTEGER_ZERO
                ? R.ok("删除数据成功") : R.failed("删除数据失败");
    }

    /**
     * 认领操作(已认领 -> 待认领)
     *
     * @param id 项目回款跟踪id
     * @return {@link Boolean}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean claim(Long id) {
        // 已认领状态 + 待锁定状态
        ProjectPayment projectPayment = baseMapper.selectOne(Wrappers.<ProjectPayment>lambdaQuery()
                .eq(ProjectPayment::getClaimStatus, ClaimStatusEnum.YES.getValue())
                .eq(ProjectPayment::getLockStatus, LockStatusEnum.NO.getValue())
                .eq(ProjectPayment::getId, id)
        );
        if (Optional.ofNullable(projectPayment).isPresent()) {
            // 更换认领状态
            projectPayment.setClaimStatus(ClaimStatusEnum.NO.getValue());
            // 认领日期自动锁定
            if (DateUtil.difference(projectPayment.getPaymentDate()) > DateUtil.THIRTY_DAYS
                    && projectPayment.getAutoLock().equals(AutoLockEnum.YES.getValue())) {
                projectPayment.setLockStatus(LockStatusEnum.YES.getValue());
            }
            // 查询条件
            LambdaQueryWrapper<ProjectPaymentClaim> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProjectPaymentClaim::getProjectPaymentId, id);
            ProjectPaymentClaim projectPaymentClaim = ProjectPaymentClaim.builder()
                    .projectPaymentId(id)
                    .build();
            // 财务数据
            if (SecurityUtils.getUser() != null) {
                if (Boolean.FALSE.equals(remotePaymentService.unClaimInner(id).getData())) {
                    throw new ServiceException("财务系统取消认领有误");
                }
            }
            return baseMapper.updateById(projectPayment) > NumberUtils.INTEGER_ZERO
                    && projectPaymentClaimMapper.delete(queryWrapper) > NumberUtils.INTEGER_ZERO
                    && projectPaymentClaimMapper.insert(projectPaymentClaim) > NumberUtils.INTEGER_ZERO;
        }
        return Boolean.TRUE;
    }

    /**
     * 认领操作(待认领 -> 已认领)
     *
     * @param dto {@link ProjectPaymentClaimDTO}
     * @return {@link Boolean}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean claim(ProjectPaymentClaimDTO dto) {
        // 财务数据
        if (SecurityUtils.getUser() != null) {
            dto.setUserId(SecurityUtils.getUser().getId());
            dto.setUsername(SecurityUtils.getUser().getName());
            if (Boolean.FALSE.equals(remotePaymentService.claimInner(dto).getData())) {
                throw new ServiceException("数字财务认领操作异常");
            }
        }
        // 1、待认领状态 + 待锁定状态
        ProjectPayment projectPayment = baseMapper.selectOne(Wrappers.<ProjectPayment>lambdaQuery()
                .eq(ProjectPayment::getClaimStatus, ClaimStatusEnum.NO.getValue())
                .eq(ProjectPayment::getLockStatus, LockStatusEnum.NO.getValue())
                .eq(ProjectPayment::getId, dto.getId())
        );
        // 2、由于认领和编辑是同一个接口 所以如果有认领数据就是更新 无认领数据就是插入
        if (Optional.ofNullable(projectPayment).isPresent()) {
            // 2.1、处理项目回款数据
            CopyOptions projectPaymentOption = CopyOptions.create()
                    .setIgnoreNullValue(true)
                    .setIgnoreError(true);
            BeanUtil.copyProperties(dto, projectPayment, projectPaymentOption);
            projectPayment.setClaimStatus(ClaimStatusEnum.YES.getValue());
            // 根据客户名称的备案人进行消息推送
            this.pushMessage(dto.getCustomerName(), projectPayment, dto.getPaymentDate(), projectPayment.getDocumentNumber());
            // 2.2、处理项目回款认领数据 后续可以采用saveOrUpdate()优化
            ProjectPaymentClaim projectPaymentClaim = projectPaymentClaimMapper.selectOne(Wrappers.<ProjectPaymentClaim>lambdaQuery()
                    .eq(ProjectPaymentClaim::getProjectPaymentId, projectPayment.getId())
            );
            // 2.1、更新项目回款跟踪
            if (Optional.ofNullable(projectPaymentClaim).isPresent()) {
                this.getIdByName(projectPaymentClaim, dto);
                CopyOptions projectPaymentClaimOption = CopyOptions.create()
                                .setIgnoreNullValue(true)
                                .setIgnoreError(true)
                                .setIgnoreProperties("id", "claimantDate", "paymentDeptId", "paymentSecondaryDeptId");
                BeanUtil.copyProperties(dto, projectPaymentClaim, projectPaymentClaimOption);
                if (SecurityUtils.getUser() != null) {
                    projectPaymentClaim.setClaimantId(SecurityUtils.getUser().getId());
                    projectPaymentClaim.setClaimantName(SecurityUtils.getUser().getName());
                } else {
                    projectPaymentClaim.setClaimantId(dto.getUserId());
                    projectPaymentClaim.setClaimantName(dto.getUsername());
                }

                return baseMapper.updateById(projectPayment) > NumberUtils.INTEGER_ZERO
                        && projectPaymentClaimMapper.updateById(projectPaymentClaim) > NumberUtils.INTEGER_ZERO;
            }
            // 无认领数据做插入操作
            projectPaymentClaim = BeanUtil.copyProperties(dto, ProjectPaymentClaim.class, "id", "claimantDate");
            // 赋值部分id
            projectPaymentClaim.setProjectPaymentId(projectPayment.getId());
            projectPaymentClaim.setPaymentDept(null);
            projectPaymentClaim.setPaymentSecondaryDept(null);
            this.getIdByName(projectPaymentClaim, dto);
            if (SecurityUtils.getUser() != null) {
                projectPaymentClaim.setClaimantId(SecurityUtils.getUser().getId());
                projectPaymentClaim.setClaimantName(SecurityUtils.getUser().getName());
            } else {
                projectPaymentClaim.setClaimantId(dto.getUserId());
                projectPaymentClaim.setClaimantName(dto.getUsername());
            }
            // 赋值认领日期
            String claimantDate = dto.getClaimantDate();
            if (CharSequenceUtil.isNotBlank(claimantDate)) {
                DateTimeFormatter df = DateTimeFormatter.ofPattern(DateUtil.DATETIME_FORMAT);
                claimantDate = dto.getClaimantDate() + DateUtil.BLACK_HOUR_MINUTE_SECOND;
                projectPaymentClaim.setClaimantDate(LocalDateTime.parse(claimantDate, df));
            }

            return baseMapper.updateById(projectPayment) > NumberUtils.INTEGER_ZERO
                    && projectPaymentClaimMapper.insert(projectPaymentClaim) > NumberUtils.INTEGER_ZERO;
        }
        return Boolean.TRUE;
    }

    /**
     * 定时任务每日更新认领时间
     *
     * @return {@link R}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public R<Void> updateLockStatus() {
        List<ProjectPayment> projectPaymentList = this.list(Wrappers.<ProjectPayment>lambdaQuery()
                .eq(ProjectPayment::getClaimStatus, ClaimStatusEnum.NO.getValue())
                .eq(ProjectPayment::getAutoLock, AutoLockEnum.YES.getValue())
        );
        if (projectPaymentList.isEmpty()) {
            return R.ok("无需要自动锁定数据");
        }

        projectPaymentList.forEach(p -> {
            if (DateUtil.difference(p.getPaymentDate()) > DateUtil.THIRTY_DAYS
                    && ClaimStatusEnum.NO.getValue().equals(p.getClaimStatus())) {
                p.setLockStatus(LockStatusEnum.YES.getValue());
            }
        });
        return this.updateBatchById(projectPaymentList) ? R.ok() : R.failed();
    }

    /**
     * 单据状态条件
     *
     * @param dto {@link ProjectPaymentDTO}
     */
    private void questionStatus(ProjectPaymentDTO dto) {
        List<String> documentStatus = dto.getDocumentStatus();
        if (CollUtil.isNotEmpty(documentStatus)) {
            List<Integer> claimStatusList = new ArrayList<>();
            List<Integer> lockStatusList = new ArrayList<>();
            List<Integer> pushStatusList = new ArrayList<>();
            documentStatus.forEach(d -> {
                claimStatusList.add(EnumUtils.getValueByName(ClaimStatusEnum.class, d));
                lockStatusList.add(EnumUtils.getValueByName(LockStatusEnum.class, d));
                pushStatusList.add(EnumUtils.getValueByName(PushStatusEnum.class, d));
            });
            dto.setClaimStatusByDocument(claimStatusList.stream().filter(Objects::nonNull).collect(Collectors.toList()));
            dto.setLockStatusByDocument(lockStatusList.stream().filter(Objects::nonNull).collect(Collectors.toList()));
            dto.setPushStatusByDocument(pushStatusList.stream().filter(Objects::nonNull).collect(Collectors.toList()));
        }
    }

    /**
     * 封装实体集合展示
     *
     * @param projectPaymentList {@link List}<{@link ProjectPayment}>
     * @return {@link List}<{@link ProjectPaymentVO}>
     */
    private List<ProjectPaymentVO> addVoToList(List<ProjectPayment> projectPaymentList) {
        List<ProjectPaymentVO> projectPaymentVOList = new ArrayList<>();
        // 1、获取 OA 的【项目台帐】
        List<ProjectAccountVO> projectAccountList = dbApi.getProjectAccountByName(projectPaymentList.stream()
                .filter(p -> Optional.ofNullable(p.getProjectPaymentClaim().getProjectName()).isPresent())
                .map(p -> p.getProjectPaymentClaim().getProjectName())
                .collect(Collectors.toList()), null);
        // 2、获取 OA 的【归属合同收款明细】
        List<ContractPaymentVO2> contractPaymentList = dbApi.getContractPaymentByNumber(projectPaymentList.stream()
                .filter(p -> Optional.ofNullable(p.getProjectPaymentClaim().getContractPayment()).isPresent())
                .map(p -> p.getProjectPaymentClaim().getContractPayment().split("~")[NumberUtils.INTEGER_ZERO])
                .collect(Collectors.toList())
        );
        // 3、获取业务板块字典值
        Map<Integer, String> businessBlockDict = dbApi.projectDict(DictConstants.BUSINESS_BLOCK_ID)
                .stream()
                .collect(Collectors.toMap(ProjectDictVo::getSelectvalue, ProjectDictVo::getSelectname, (a, b) -> a));
        // 4、获取技术类型字典值
        Map<Integer, String> skillTypeDict = dbApi.projectDict(DictConstants.SKILL_TYPE_ID)
                .stream()
                .collect(Collectors.toMap(ProjectDictVo::getSelectvalue, ProjectDictVo::getSelectname, (a, b) -> a));
        // 5、展示封装
        projectPaymentList.forEach(p -> {
            ProjectPaymentVO projectPaymentVO = BeanUtil.copyProperties(p, ProjectPaymentVO.class);
            // 5.1、枚举与金额处理
            projectPaymentVO.setPaymentCompanyTxt(EnumUtils.getNameByValue(AttributableSubjectEnum.class, String.valueOf(p.getPaymentCompany())));
            projectPaymentVO.setClaimStatusTxt(EnumUtils.getNameByValue(ClaimStatusEnum.class, p.getClaimStatus()));
            projectPaymentVO.setLockStatusTxt(EnumUtils.getNameByValue(LockStatusEnum.class, p.getLockStatus()));
            projectPaymentVO.setPushStatusTxt(EnumUtils.getNameByValue(PushStatusEnum.class, p.getPushStatus()));
            projectPaymentVO.setPaymentAmount(MoneyUtil.getInstance().transType(p.getPaymentAmount()));
            // 5.2、明细认领处理
            ProjectPaymentClaim projectPaymentClaim = p.getProjectPaymentClaim();
            // 5.2.1、OA【项目台帐】
            String projectName = projectPaymentClaim.getProjectName();
            if (CharSequenceUtil.isNotBlank(projectName)) {
                projectAccountList.forEach(project -> {
                    if (project.getProjectName().equals(projectName)) {
                        projectPaymentVO.setProjectId(project.getId());
                        projectPaymentVO.setProjectNumber(project.getProjectNumber());
                    }
                });
            }
            // 5.2.2、OA【归属合同明细】
            String contractPayment = projectPaymentClaim.getContractPayment();
            if (CharSequenceUtil.isNotBlank(contractPayment)) {
                String contractNumber = contractPayment.split("~")[NumberUtils.INTEGER_ZERO];
                projectPaymentVO.setContractNumber(contractNumber);
                contractPaymentList.forEach(c -> {
                    if (c.getContractNumber().equals(contractNumber)) {
                        projectPaymentVO.setContractId(c.getId());
                    }
                });
            }
            // 5.2.3、字典值设置
            if (Optional.ofNullable(projectPaymentClaim.getBusinessBlock()).isPresent()) {
                projectPaymentVO.setBusinessBlock(projectPaymentClaim.getBusinessBlock());
                projectPaymentVO.setBusinessBlockTxt(businessBlockDict.get(projectPaymentClaim.getBusinessBlock()));
            }
            if (Optional.ofNullable(projectPaymentClaim.getSkillType()).isPresent()) {
                projectPaymentVO.setSkillType(projectPaymentClaim.getSkillType());
                projectPaymentVO.setSkillTypeTxt(skillTypeDict.get(projectPaymentClaim.getSkillType()));
            }

            projectPaymentVOList.add(this.setProjectPaymentClaim(projectPaymentVO, projectPaymentClaim));
        });

        return projectPaymentVOList;
    }

    /**
     * 根据传入的名字赋值id
     *
     * @param projectPaymentClaim    {@link ProjectPaymentClaim}
     * @param projectPaymentClaimDTO {@link ProjectPaymentClaimDTO}
     */
    private void getIdByName(ProjectPaymentClaim projectPaymentClaim, ProjectPaymentClaimDTO projectPaymentClaimDTO) {
        // 1、认领日期
        projectPaymentClaim.setClaimantDate(LocalDateTime.now());
        // 2、归属合同收款明细
        String contractPayment = projectPaymentClaimDTO.getContractPayment();
        List<ContractPaymentVO2> contractPaymentList = dbApi.getContractPayment(null);
        if (CharSequenceUtil.isEmpty(contractPayment) || CollUtil.isEmpty(contractPaymentList)) {
            projectPaymentClaim.setContractPaymentId(null);
        } else if (!contractPayment.equals(projectPaymentClaim.getContractPayment())) {
            contractPaymentList.stream()
                    .filter(c -> CharSequenceUtil.isNotBlank(c.getContractNumber()))
                    .filter(c -> CharSequenceUtil.isNotBlank(c.getPaymentName()))
                    .filter(c -> Optional.ofNullable(c.getPrice()).isPresent())
                    .forEach(c -> {
                        // 前端返回: 合同编码contractNumber~款项名称paymentName~合同名称contractName~款项金额price
                        String[] contract = contractPayment.split("~");
                        if (c.getContractNumber().equals(contract[NumberUtils.INTEGER_ZERO])
                                && c.getPaymentName().equals(contract[NumberUtils.INTEGER_ONE])
                                && c.getPrice().toString().equals(contract[3])) {
                            projectPaymentClaim.setContractPaymentId(c.getId());
                        }
                    });
        }
        // 3、项目名称
        String projectName = projectPaymentClaim.getProjectName();
        if (CharSequenceUtil.isNotBlank(projectName)) {
            Map<String, Long> projectMap = dbApi.getProjectAccount(projectName)
                    .stream().collect(Collectors.toMap(ProjectAccountVO::getProjectName, ProjectAccountVO::getId, (a, b) -> a));
            if (CharSequenceUtil.isEmpty(projectPaymentClaimDTO.getProjectName())) {
                projectPaymentClaim.setProjectId(null);
            } else if (!projectPaymentClaimDTO.getProjectName().equals(projectName)) {
                projectPaymentClaim.setProjectId(projectMap.get(projectName));
            }
        }
        // 4、部门id
        if (CharSequenceUtil.isEmpty(projectPaymentClaimDTO.getPaymentDept())) {
            projectPaymentClaim.setPaymentDeptId(null);
        } else if (!projectPaymentClaimDTO.getPaymentDept().equals(projectPaymentClaim.getPaymentDept())) {
            // 4.1、中台部门组织架构
            Map<String, Long> firstDeptMap = remoteOutMultiDeptService
                    .getDeptList("行政组织", null, null)
                    .getData()
                    .stream()
                    .collect(Collectors.toMap(MultiDimensionDeptDto::getName, MultiDimensionDeptDto::getDeptId, (a, b) -> a));
            // 4.1.1、一级部门
            String paymentDept = projectPaymentClaimDTO.getPaymentDept();
            if (CharSequenceUtil.isNotBlank(paymentDept)) {
                projectPaymentClaim.setPaymentDept(paymentDept);
                projectPaymentClaim.setPaymentDeptId(firstDeptMap.get(paymentDept));
                // 4.1.2、二级部门
                String paymentSecondaryDept = projectPaymentClaimDTO.getPaymentSecondaryDept();
                if (CharSequenceUtil.isNotBlank(paymentSecondaryDept)) {
                    projectPaymentClaim.setPaymentSecondaryDept(paymentSecondaryDept);
                    projectPaymentClaim.setPaymentSecondaryDeptId(firstDeptMap.get(paymentSecondaryDept));
                }
            }
        }

    }

    /**
     * 消息推送前提判断客户名称不能为空
     *
     * @param customerName 客户名称
     * @param projectPayment {@link ProjectPayment}
     * @param paymentDate 收款日期
     * @param documentNumber 单据编号
     */
    private void pushMessage(String customerName, ProjectPayment projectPayment, String paymentDate, String documentNumber) {
        if (CharSequenceUtil.isNotBlank(customerName)) {
            Map<String, CustomerAccountVO> customerAccountMap = dbApi.getCustomerAccount(CharSequenceUtil.EMPTY)
                    .stream()
                    .collect(Collectors.toMap(CustomerAccountVO::getCustomerName, a -> a, (a, b) -> a));
            CustomerAccountVO customerAccountVO = customerAccountMap.get(customerName);
            if (Optional.ofNullable(customerAccountVO).isPresent()) {
                this.pushMessage(paymentDate, documentNumber, customerAccountVO.getRecordManId(), customerAccountVO.getRecordMan());
                projectPayment.setRecordManId(customerAccountVO.getRecordManId());
            }
        }
    }

    /**
     * 消息推送
     *
     * @param paymentDate    收款日期
     * @param documentNumber 单据编号
     * @param targetId       目标人员id
     * @param targetName     目标人员姓名
     */
    private void pushMessage(String paymentDate, String documentNumber, Long targetId, String targetName) {
        // 没有目标人信息直接返回
        if (!Optional.ofNullable(targetId).isPresent() || !Optional.ofNullable(targetName).isPresent()) {
            return;
        }

        // 消息内容
        final String content = "你有一笔【" + paymentDate + "】的项目回款【" + documentNumber + "】等待认领，请及时处理~";
        // 消息实体
        MailModel model = new MailModel();
        model.setSource(SourceEnum.DIGITAL_FINANCE.getValue());
        model.setType(MsgTypeEnum.TEXT_MSG.getValue());
        model.setTitle("数字财务项目回款待认领");
        model.setContent(content);
        model.setSenderId(portalAppId);
        model.setSender("数字化财务系统");
        model.setTargetType(TargetTypeEnum.USERS.getValue());
        model.setRedirectUrl(projectPaymentUrl + "?documentNumber=" + documentNumber);
        // 接受消息的人列表
        List<BcpMessageTargetDTO> list = new ArrayList<>();

        BcpMessageTargetDTO person = new BcpMessageTargetDTO();
        person.setTargetId(targetId.toString());
        person.setTargetName(targetName);
        list.add(person);

        model.setTargetList(list);

        try {
            remoteMailService.sendMsg(model);
        } catch (Exception e) {
            log.info("中台推送消息失败, 对应的目标人员id为: {}, 姓名为: {}", targetId, targetName);
        }
    }

    /**
     * 回款认领基础字段赋值
     *
     * @param vo {@link ProjectPaymentVO}
     * @param projectPaymentClaim {@link ProjectPaymentClaim}
     * @return {@link ProjectPaymentVO}
     */
    private ProjectPaymentVO setProjectPaymentClaim(ProjectPaymentVO vo, ProjectPaymentClaim projectPaymentClaim) {
        String belongingAreaId = projectPaymentClaim.getBelongingAreaId();
        vo.setBelongingAreaId(belongingAreaId);
        // 1、赋值归属领域
        if (CharSequenceUtil.isNotBlank(belongingAreaId)) {
            List<String> belongingAreaList = Arrays.stream(projectPaymentClaim.getBelongingArea().split(StrPool.COMMA))
                    .collect(Collectors.toList());
            int size = belongingAreaList.size();
            if (NumberUtils.INTEGER_ONE.equals(size)) {
                projectPaymentClaim.setBelongingArea(belongingAreaList.get(NumberUtils.INTEGER_ZERO));
            } else {
                projectPaymentClaim.setBelongingArea(belongingAreaList.get(NumberUtils.INTEGER_ONE));
            }
        }
        vo.setClaimantDate(projectPaymentClaim.getClaimantDate());
        vo.setContractPayment(projectPaymentClaim.getContractPayment());
        vo.setProjectCollection(projectPaymentClaim.getProjectCollection());
        vo.setProjectCollectionTxt(EnumUtils.getNameByValue(CollectionWithinBudgetEnum.class, projectPaymentClaim.getProjectCollection()));
        vo.setBusinessLine(projectPaymentClaim.getBusinessLine());
        vo.setBusinessLineTxt(EnumUtils.getNameByValue(BusinessLineEnum.class, projectPaymentClaim.getBusinessLine()));
        vo.setBelongingArea(projectPaymentClaim.getBelongingArea());
        vo.setSalesmanUserName(projectPaymentClaim.getSalesmanUserName());
        vo.setProjectName(projectPaymentClaim.getProjectName());
        vo.setContractName(projectPaymentClaim.getContractName());
        vo.setPaymentDept(projectPaymentClaim.getPaymentDept());
        vo.setPaymentSecondaryDept(projectPaymentClaim.getPaymentSecondaryDept());
        vo.setClaimantName(projectPaymentClaim.getClaimantName());
        return vo;
    }

}
