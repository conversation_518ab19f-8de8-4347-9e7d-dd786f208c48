package com.gok.pboot.pms.service;

import com.gok.components.common.user.PigxUser;
import com.gok.pboot.pms.common.base.StatisticsPage;
import com.gok.pboot.pms.entity.dto.DdOrderQueryDTO;
import com.gok.pboot.pms.entity.vo.DdFlightTicketOrderFindPageVO;
import com.gok.pboot.pms.entity.vo.DdHotelOrderFindPageVO;
import com.gok.pboot.pms.entity.vo.DdOrderImportResultVO;
import com.gok.pboot.pms.entity.vo.DdVehicleOrderFindPageVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 滴滴订单管理 服务接口
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface IDdOrderService {

    /**
     * 分页查询机票订单
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    StatisticsPage<DdFlightTicketOrderFindPageVO> findFlightTicketPageList(StatisticsPage<DdFlightTicketOrderFindPageVO> page, Map<String, Object> queryDTO);

    /**
     * 分页查询酒店订单
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    StatisticsPage<DdHotelOrderFindPageVO> findHotelPageList(StatisticsPage<DdHotelOrderFindPageVO> page, Map<String, Object>  queryDTO);

    /**
     * 分页查询用车订单
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    StatisticsPage<DdVehicleOrderFindPageVO> findVehiclePageList(StatisticsPage<DdVehicleOrderFindPageVO> page, Map<String, Object>  queryDTO);

    /**
     * 导入订单数据
     *
     * @param file Excel文件
     * @return 导入结果
     */
    DdOrderImportResultVO importOrders(MultipartFile file);

    /**
     * 生成报销
     *
     * @param queryDTO 查询条件
     */
    void generateReimbursement(DdOrderQueryDTO queryDTO, PigxUser user);
} 