package com.gok.pboot.pms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.enumeration.OrderTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 滴滴订单通用实体
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("")
public class DdCommonOrder {
    
    /**
     * 订单ID
     */
    private Long id;
    
    /**
     * 订单类型
     */
    private OrderTypeEnum orderType;
    
    /**
     * 订单编号
     */
    private String orderNo;
    
    /**
     * 企业实付金额
     */
    private BigDecimal companyActualPayment;
    
    /**
     * 乘客ID
     */
    private Long passengerId;
    
    /**
     * 乘客工号
     */
    private String passengerEmployeeNo;
    
    /**
     * 乘客姓名
     */
    private String passengerName;
    
    /**
     * 预订人ID
     */
    private Long bookingUserId;
    
    /**
     * 预订人工号
     */
    private String bookingEmployeeNo;
    
    /**
     * 预订人姓名
     */
    private String bookingUserName;

    /**
     * 所属公司ID
     */
    private Long companyId;
    
    /**
     * 所属公司
     */
    private String companyName;
    
    /**
     * 预订人部门名称
     */
    private String bookingDeptName;
    
    /**
     * 成本中心名称
     */
    private String costCenterName;
    
    /**
     * 成本中心ID
     */
    private Long costCenterId;

    /**
     * OA部门ID
     */
    private Long oaDeptId;
    
    /**
     * 所属项目
     */
    private String projectName;
    
    /**
     * 项目ID
     */
    private Long projectId;
    
    /**
     * 项目状态
     */
    private Integer projectStatus;
    
    /**
     * 报销状态
     */
    private Integer initiationStatus;
    
    /**
     * 发起日期
     */
    private LocalDate initiationDate;

    /**
     * 请求ID
     */
    private Long requestId;
    
    /**
     * 创建时间
     */
    private LocalDateTime ctime;
    
    /**
     * 更新时间
     */
    private LocalDateTime mtime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String modifier;
} 