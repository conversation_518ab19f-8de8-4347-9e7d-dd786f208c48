package com.gok.pboot.pms.didi.entity.dto;

import lombok.Data;

import java.util.List;

/**
 * 滴滴用户同步请求DTO
 * 根据滴滴企业版API文档构建：/river/Member/single
 *
 * <AUTHOR>
 * @date 2025/08/04
 */
@Data
public class DidiUserSyncReq {

    /**
     * 申请应用时分配的AppKey
     * 必填
     */
    private String client_id;

    /**
     * 授权后的access token
     * 必填
     */
    private String access_token;

    /**
     * 企业ID
     * 必填
     */
    private String company_id;

    /**
     * 当前时间戳(精确到秒级)
     * 必填
     */
    private Integer timestamp;

    /**
     * 签名
     * 必填
     */
    private String sign;

    /**
     * 是否发短信配置
     * 非必填
     * 枚举值数字 0 不发送，1 发送
     * 员工添加成功后发短信（包含双向确认短信）
     * 默认值为0
     */
    private Integer send_message;

    /**
     * 是否含有证件信息
     * 非必填
     * 是否含有证件信息，当传证件信息时，此字符传1，其他情况不传或传0
     */
    private Integer has_card_info;

    /**
     * 员工信息
     * 必填
     * JSON字符串格式
     */
    private String data;

    /**
     * 员工信息数据对象（用于构建data字段）
     */
    @Data
    public static class UserData {

        /**
         * 员工信息类型
         * 非必填
         * 枚举值数字 0：手机号，1：工号，2：邮箱
         * 默认为0
         */
        private Integer member_type;

        /**
         * 员工手机号
         * 非必填
         * member_type 为0时必传
         * 使用海外手机号时输入格式为 +90 137****0001，国内可以不加区号
         */
        private String phone;

        /**
         * 员工姓名
         * 必填
         */
        private String realname;

        /**
         * 员工工号
         * 非必填
         * member_type 为1时必传
         * 员工在公司的员工号
         */
        private String employee_number;

        /**
         * 邮箱
         * 非必填
         * member_type 为2时必传
         */
        private String email;

        /**
         * 系统角色
         * 非必填
         * 枚举值数字 0 车辆预定人员，1 普通管理员，2 超级管理员
         */
        private Integer system_role;

        /**
         * 角色
         * 非必填
         * 可以通过角色API获取对应的ID
         */
        private String role_ids;

        /**
         * 员工直属上级的手机号码
         * 非必填
         * 直属上级可在审批流中担任审批人
         * immediate_superior_phone与immediate_superior_eid以手机号优先
         * 使用海外手机号时输入格式为 +90 137****0001，国内可以不加区号
         */
        private String immediate_superior_phone;

        /**
         * 直属上级邮箱
         * 非必填
         */
        private String immediate_superior_email;

        /**
         * 员工直属上级的员工编号
         * 非必填
         * 直属上级可在审批流中担任审批人
         */
        private String immediate_superior_employee_number;

        /**
         * 直属上级 ID
         * 非必填
         */
        private Integer immediate_superior_memberID;

        /**
         * 常驻地中文
         * 非必填
         */
        private String residentsname;

        /**
         * 常驻地ID
         * 非必填
         * 多个使用"_"连接
         * residentsname和residents_ids同时存在时以residentsname为准
         */
        private String residents_ids;

        /**
         * 是否企业支付余额
         * 非必填
         * 枚举值数字 0 否，1 是
         */
        private Integer use_company_money;

        /**
         * 每月配额
         * 非必填
         * 单位元 0 不限
         */
        private String total_quota;

        /**
         * 叫车时备注信息是否必填
         * 非必填
         * 枚举值数字 0 选填，1 必填，2 按制度填写
         */
        private String is_remark;

        /**
         * 所在部门ID
         * 非必填
         * budget_center_id与out_budget_id同时存在时，以budget_center_id为准
         */
        private Long budget_center_id;

        /**
         * 客户部门CODE
         * 非必填
         */
        private String out_budget_id;

        /**
         * 所在兼岗部门ID
         * 非必填
         * con_department_ids与con_department_codes都存在时，以con_department_ids为准
         * 多个使用"_"连接
         */
        private String con_department_ids;

        /**
         * 所在兼岗部门CODE（同部门新增修改的out_budget_id）
         * 非必填
         * con_department_ids与con_department_codes都存在时，以con_department_ids为准
         * 多个使用"_"连接
         */
        private String con_department_codes;

        /**
         * 用车制度ID数组
         * 非必填
         * 制度ID；通过制度列表接口查询；多个用 _ 连接；默认为空
         * 若该员工的所有制度都是在es后台通过部门/职级/全员方式分配，则员工身上的制度字段不用传
         * 同时注意检查use_company_money字段是否传输，制度和企业支付权限都有才能企业支付
         */
        private String regulation_id;

        /**
         * 所在项目ID
         * 非必填
         * 可以填多个，以_分隔
         * 通过成本中心查询api获取id（类型为2）
         * 元素上限500个
         */
        private String project_ids;

        /**
         * 项目信息
         * 非必填
         * 人员上绑定的项目信息
         * 将project_codes_detail的值转为 json 字符
         * project_ids与project_code_detail同时有值时，以project_ids为准
         * 元素上限500个（目前开放平台内部是以此字段换project_ids来设置值）
         */
        private String project_codes_detail;

        /**
         * 所在公司主体id
         * 非必填
         */
        private String legal_entity_id;

        /**
         * 外部所在公司主体id
         * 非必填
         */
        private String out_legal_entity_id;

        /**
         * 职级id
         * 非必填
         */
        private String rank_id;

        /**
         * 外部职级 ID
         * 非必填
         */
        private String out_rank_id;

        /**
         * 英文姓
         * 非必填
         * 同lastname
         */
        private String english_surname;

        /**
         * 英文名
         * 非必填
         * 同firstname 有middlename时 english_name=firstname middlename
         */
        private String english_name;

        /**
         * 昵称
         * 非必填
         */
        private String nickname;

        /**
         * 性别
         * 非必填
         * 枚举值数字 0 未知 1 男 2 女
         */
        private Integer sex;

        /**
         * 出生日期
         * 非必填
         * 格式2000-01-01
         * 注：
         * 1、若采用AES256整体加密，此字段需明文传输，无需单独再加密
         * 2、若不整体加密传输时，此字段只可采用AES128加密传输
         * 3、若采用AES128整体加密，此字段仍需采用AES128单独加密（存在历史客户原因）
         */
        private String birth_date;

        /**
         * 证件信息
         * 非必填
         */
        private List<DidiCardInfo> card_list;
    }
}
