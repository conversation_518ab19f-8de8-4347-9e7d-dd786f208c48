package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 项目与滴滴项目关联表
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dd_project_relation")
public class DdProjectRelation {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目编码
     */
    private String projectNo;

    /**
     * 滴滴项目id
     */
    private String didiProjectId;

    /**
     * 同步时间
     */
    private LocalDateTime syncTime;

}
