package com.gok.pboot.pms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.entity.domain.DdProjectRelation;
import com.gok.pboot.pms.mapper.DdProjectRelationMapper;
import com.gok.pboot.pms.service.IDdProjectRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 项目与滴滴项目关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Service
public class DdProjectRelationServiceImpl extends ServiceImpl<DdProjectRelationMapper, DdProjectRelation> implements IDdProjectRelationService {

    @Override
    public boolean saveOrUpdateRelation(Long projectId, String projectNo, String didiProjectId) {
        try {
            // 先查询是否已存在关联关系
            DdProjectRelation existingRelation = baseMapper.selectByProjectId(projectId);
            
            if (existingRelation != null) {
                // 更新现有关联关系
                existingRelation.setDidiProjectId(didiProjectId);
                existingRelation.setSyncTime(LocalDateTime.now());
                return updateById(existingRelation);
            } else {
                // 创建新的关联关系
                DdProjectRelation newRelation = new DdProjectRelation()
                        .setProjectId(projectId)
                        .setProjectNo(projectNo)
                        .setDidiProjectId(didiProjectId)
                        .setSyncTime(LocalDateTime.now());
                return save(newRelation);
            }
        } catch (Exception e) {
            log.error("保存或更新项目与滴滴项目关联关系失败，项目ID: {}, 项目编码: {}, 滴滴项目ID: {}", 
                    projectId, projectNo, didiProjectId, e);
            return false;
        }
    }

    @Override
    public DdProjectRelation getByProjectId(Long projectId) {
        return baseMapper.selectByProjectId(projectId);
    }

    @Override
    public DdProjectRelation getByProjectNo(String projectNo) {
        return baseMapper.selectByProjectNo(projectNo);
    }

    @Override
    public DdProjectRelation getByDidiProjectId(String didiProjectId) {
        return baseMapper.selectByDidiProjectId(didiProjectId);
    }

    @Override
    public boolean updateSyncTime(Long projectId) {
        try {
            int result = baseMapper.updateSyncTimeByProjectId(projectId);
            return result > 0;
        } catch (Exception e) {
            log.error("更新同步时间失败，项目ID: {}", projectId, e);
            return false;
        }
    }

}
