package com.gok.pboot.pms.didi.client;

import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.Post;
import com.gok.pboot.pms.didi.entity.dto.*;
import com.gok.pboot.pms.didi.interceptor.DidiInterceptor;
import org.springframework.stereotype.Component;

/**
 * 滴滴客户端
 *
 * <AUTHOR>
 * @date 2025/08/04
 */
@BaseRequest(
        baseURL = "https://api.es.xiaojukeji.com",
        contentType = "application/json"
)
@Component
public interface DidiClient {

    /**
     * 获取token
     *
     * @param authReq authReq
     * @return {@link DidiAuthRes }
     */
    @Post("/river/Auth/authorize")
    DidiAuthRes getToken(@Body DidiAuthReq authReq);

    /**
     * 同步项目信息至滴滴
     *
     * @param projectSyncReq 项目同步请求
     * @return {@link DidiResult < DidiProjectSyncRes >}
     */
    @Post(url = "/river/BudgetCenter/add", interceptor = DidiInterceptor.class)
    DidiResult<DidiProjectSyncRes> addProject(@Body DidiProjectSyncReq projectSyncReq);

    /**
     * 更新项目信息
     *
     * @param projectSyncReq 项目同步请求
     * @return {@link DidiResult<DidiProjectSyncRes>}
     */
    @Post(url = "/river/BudgetCenter/edit", interceptor = DidiInterceptor.class)
    DidiResult<DidiProjectSyncRes> updateProject(@Body DidiProjectSyncReq projectSyncReq);

    /**
     * 删除项目信息
     *
     * @param projectCode 项目编号
     * @return {@link DidiResult<DidiProjectSyncRes>}
     */
    @Post(url = "/river/Project/delete", interceptor = DidiInterceptor.class)
    DidiResult<DidiProjectSyncRes> deleteProject(@Body String projectCode);

    /**
     * 同步用户信息至滴滴
     *
     * @param syncReq 同步请求对象
     * @return {@link DidiResult<DidiUserSyncRes>}
     */
    @Post(url = "/river/Member/single", interceptor = DidiInterceptor.class)
    DidiResult<DidiUserSyncRes> addUser(@Body DidiUserSyncReq syncReq);
}
