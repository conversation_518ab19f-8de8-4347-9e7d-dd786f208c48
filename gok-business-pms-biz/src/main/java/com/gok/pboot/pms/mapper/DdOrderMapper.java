package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.entity.DdFlightTicketOrder;
import com.gok.pboot.pms.entity.DdHotelOrder;
import com.gok.pboot.pms.entity.DdVehicleOrder;
import com.gok.pboot.pms.entity.dto.DdOrderQueryDTO;
import com.gok.pboot.pms.entity.vo.DdFlightTicketOrderFindPageVO;
import com.gok.pboot.pms.entity.vo.DdHotelOrderFindPageVO;
import com.gok.pboot.pms.entity.vo.DdVehicleOrderFindPageVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 滴滴订单管理 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface DdOrderMapper {

    /**
     * 分页查询机票订单
     *
     * @param page     分页参数
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<DdFlightTicketOrderFindPageVO> findFlightTicketPageList(Page<DdFlightTicketOrderFindPageVO> page, @Param("queryDTO") Map<String, Object> queryDTO);

    /**
     * 分页查询酒店订单
     *
     * @param page     分页参数
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<DdHotelOrderFindPageVO> findHotelPageList(Page<DdHotelOrderFindPageVO> page, @Param("queryDTO") Map<String, Object> queryDTO);

    /**
     * 分页查询用车订单
     *
     * @param page     分页参数
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<DdVehicleOrderFindPageVO> findVehiclePageList(Page<DdVehicleOrderFindPageVO> page, @Param("queryDTO") Map<String, Object> queryDTO);

    /**
     * 批量插入机票订单
     *
     * @param entities 机票订单实体列表
     * @return 影响行数
     */
    int batchInsertFlightTicketOrders(@Param("list") List<DdFlightTicketOrder> entities);

    /**
     * 批量插入酒店订单
     *
     * @param entities 酒店订单实体列表
     * @return 影响行数
     */
    int batchInsertHotelOrders(@Param("list") List<DdHotelOrder> entities);

    /**
     * 批量插入用车订单
     *
     * @param entities 用车订单实体列表
     * @return 影响行数
     */
    int batchInsertVehicleOrders(@Param("list") List<DdVehicleOrder> entities);

    /**
     * 批量更新机票订单
     *
     * @param entities 实体
     * @return {@link Integer }
     */
    int batchUpdateFlightTicketOrder(@Param("list") List<DdFlightTicketOrder> entities);

    /**
     * 批量更新酒店订单
     *
     * @param entities 实体
     * @return {@link Integer }
     */
    int batchUpdateHotelOrder(@Param("list") List<DdHotelOrder> entities);

    /**
     * 批量更新用车订单
     *
     * @param entities 实体
     * @return {@link Integer }
     */
    int batchUpdateVehicleOrder(@Param("list") List<DdVehicleOrder> entities);

    /**
     * 查询可发起报销的机票订单
     *
     * @param queryDTO    查询条件
     * @param selectedIds 已选择的订单ID列表
     * @return 机票订单列表
     */
    List<DdFlightTicketOrder> findCanInitiateFlightTicketOrders(@Param("queryDTO") DdOrderQueryDTO queryDTO, @Param("selectedIds") List<Long> selectedIds);

    /**
     * 查询可发起报销的酒店订单
     *
     * @param queryDTO    查询条件
     * @param selectedIds 已选择的订单ID列表
     * @return 酒店订单列表
     */
    List<DdHotelOrder> findCanInitiateHotelOrders(@Param("queryDTO") DdOrderQueryDTO queryDTO, @Param("selectedIds") List<Long> selectedIds);

    /**
     * 查询可发起报销的用车订单
     *
     * @param queryDTO    查询条件
     * @param selectedIds 已选择的订单ID列表
     * @return 用车订单列表
     */
    List<DdVehicleOrder> findCanInitiateVehicleOrders(@Param("queryDTO") DdOrderQueryDTO queryDTO, @Param("selectedIds") List<Long> selectedIds);

    /**
     * 获取的已存在的机票订单
     *
     * @param orderNoSet 订单集
     * @return {@link Set }<{@link String }>
     */
    List<DdFlightTicketOrder> getFlightTicketOrders(@Param("orderNoSet") Set<String> orderNoSet);


    /**
     * 获取已存在的酒店订单
     *
     * @param orderNoSet 订单无集
     * @return {@link List }<{@link DdHotelOrder }>
     */
    List<DdHotelOrder> getHotelOrders(@Param("orderNoSet") Set<String> orderNoSet);

    /**
     * 获取已存在的用车订单
     *
     * @param orderNoSet 订单无集
     * @return {@link List }<{@link DdVehicleOrder }>
     */
    List<DdVehicleOrder> getVehicleOrders(@Param("orderNoSet") Set<String> orderNoSet);


    /**
     * 机票订单统计
     *
     * @param queryDTO 查询 dto
     * @return {@link Map }<{@link String }, {@link BigDecimal }>
     */
    Map<String, BigDecimal> flightTicketOrderStatistics(@Param("queryDTO") Map<String, Object>  queryDTO);

    /**
     * 酒店订单统计
     *
     * @param queryDTO 订单查询 dto
     * @return {@link Map }<{@link String }, {@link BigDecimal }>
     */
    Map<String, BigDecimal> hotelOrderStatistics(@Param("queryDTO") Map<String, Object>  queryDTO);

    /**
     * 用车订单统计
     *
     * @param queryDTO 订单查询 dto
     * @return {@link Map }<{@link String }, {@link BigDecimal }>
     */
    Map<String, BigDecimal> vehicleOrderStatistics(@Param("queryDTO") Map<String, Object>  queryDTO);
}