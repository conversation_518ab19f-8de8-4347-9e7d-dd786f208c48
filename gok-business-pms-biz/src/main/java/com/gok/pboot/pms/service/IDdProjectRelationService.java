package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.entity.domain.DdProjectRelation;

/**
 * 项目与滴滴项目关联表 服务类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface IDdProjectRelationService extends IService<DdProjectRelation> {

    /**
     * 保存或更新项目与滴滴项目的关联关系
     *
     * @param projectId     项目ID
     * @param projectNo     项目编码
     * @param didiProjectId 滴滴项目ID
     * @return 是否成功
     */
    boolean saveOrUpdateRelation(Long projectId, String projectNo, String didiProjectId);

    /**
     * 根据项目ID查询关联关系
     *
     * @param projectId 项目ID
     * @return 关联关系
     */
    DdProjectRelation getByProjectId(Long projectId);

    /**
     * 根据项目编码查询关联关系
     *
     * @param projectNo 项目编码
     * @return 关联关系
     */
    DdProjectRelation getByProjectNo(String projectNo);

    /**
     * 根据滴滴项目ID查询关联关系
     *
     * @param didiProjectId 滴滴项目ID
     * @return 关联关系
     */
    DdProjectRelation getByDidiProjectId(String didiProjectId);

    /**
     * 更新同步时间
     *
     * @param projectId 项目ID
     * @return 是否成功
     */
    boolean updateSyncTime(Long projectId);

}
